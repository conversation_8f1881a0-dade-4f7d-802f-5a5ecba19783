#!/usr/bin/env python3
"""
Independent brute force executor service.
Polls database for pending tasks and executes them.
"""

import asyncio
import time
import yaml
import requests
import signal
import sys
from datetime import datetime, timezone
from typing import List, Tuple, Optional
from playwright.async_api import async_playwright

from models import (
    SessionLocal, BruteForceTask, BruteForceResult, TaskLog, TaskStatus
)


class BruteForceExecutor:
    """Executor for brute force tasks with database integration."""
    
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.db = SessionLocal()
        self.task = None
        self.cancelled = False
    
    def __del__(self):
        """Clean up database connection."""
        if self.db:
            self.db.close()
    
    def log_message(self, level: str, message: str, username: str = None, password: str = None):
        """Log a message to the database."""
        log_entry = TaskLog(
            task_id=self.task_id,
            level=level,
            message=message,
            username_attempted=username,
            password_attempted=password
        )
        self.db.add(log_entry)
        self.db.commit()
        print(f"[{level}] {message}")
    
    def update_task_status(self, status: TaskStatus, error_message: str = None):
        """Update task status in database."""
        if not self.task:
            return
        
        self.task.status = status.value
        if status == TaskStatus.RUNNING and not self.task.started_at:
            self.task.started_at = datetime.now(timezone.utc)
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            self.task.completed_at = datetime.now(timezone.utc)
        
        if error_message:
            self.task.error_message = error_message
        
        self.db.commit()
    
    def update_progress(self, current_attempts: int, total_attempts: int = None):
        """Update task progress."""
        if not self.task:
            return
        
        self.task.current_attempts = current_attempts
        if total_attempts is not None:
            self.task.total_attempts = total_attempts
        
        self.db.commit()
    
    def add_successful_result(self, username: str, password: str, response_details: str = None):
        """Add a successful brute force result."""
        result = BruteForceResult(
            task_id=self.task_id,
            username=username,
            password=password,
            response_details=response_details
        )
        self.db.add(result)
        
        # Update task
        if self.task:
            self.task.successful_attempts += 1
            self.task.found_credentials = True
        
        self.db.commit()
    
    def check_target_connectivity(self) -> bool:
        """Check if target is accessible using the task URL."""
        try:
            # Use the actual URL from the task, not from template
            target_url = self.task.url

            self.log_message("INFO", f"Checking connectivity to: {target_url}")

            response = requests.get(target_url, timeout=10, allow_redirects=True)
            if response.status_code < 400:
                self.log_message("INFO", f"Target is accessible (Status: {response.status_code})")
                return True
            else:
                self.log_message("ERROR", f"Target returned error status: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            self.log_message("ERROR", "Connection timeout: Target is not responding")
            return False
        except requests.exceptions.ConnectionError:
            self.log_message("ERROR", "Connection error: Cannot reach target")
            return False
        except Exception as e:
            self.log_message("ERROR", f"Connectivity check failed: {str(e)}")
            return False
    
    def load_dictionary_files(self, username_file: str, password_file: str) -> Tuple[List[str], List[str]]:
        """Load username and password dictionaries."""
        try:
            with open(username_file, 'r', encoding='utf-8') as f:
                usernames = [line.strip() for line in f if line.strip()]
            
            with open(password_file, 'r', encoding='utf-8') as f:
                passwords = [line.strip() for line in f if line.strip()]
            
            self.log_message("INFO", f"Loaded {len(usernames)} usernames and {len(passwords)} passwords")
            return usernames, passwords
            
        except Exception as e:
            self.log_message("ERROR", f"Failed to load dictionary files: {str(e)}")
            raise
    
    async def run_single_attempt(self, config_file_path: str, username: str, password: str) -> bool:
        """Run a single brute force attempt."""
        try:
            print(f"[DEBUG] 🔐 开始尝试凭据: {username} / {password}")
            print(f"[DEBUG] 📄 加载配置文件: {config_file_path}")

            with open(config_file_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            print(f"[DEBUG] ✅ 配置文件加载成功，包含 {len(config.get('steps', {}))} 个步骤")

            async with async_playwright() as p:
                print(f"[DEBUG] 🌐 启动浏览器 (headless模式)")
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                print(f"[DEBUG] 📄 创建新页面")

                try:
                    steps = config.get('steps', {})

                    # Handle both dict and list formats
                    if isinstance(steps, dict):
                        step_configs = list(steps.items())  # 保留步骤名称
                        print(f"[DEBUG] 📋 检测到字典格式步骤，共 {len(step_configs)} 个")
                    elif isinstance(steps, list):
                        step_configs = [(f"step_{i}", step) for i, step in enumerate(steps)]
                        print(f"[DEBUG] 📋 检测到列表格式步骤，共 {len(step_configs)} 个")
                    else:
                        step_configs = []
                        print(f"[DEBUG] ⚠️  未识别的步骤格式")

                    for step_name, step_config in step_configs:
                        if self.cancelled:
                            print(f"[DEBUG] 🚫 任务已取消，停止执行")
                            return False

                        action = step_config.get('action')
                        target = step_config.get('target', {})
                        parameters = step_config.get('parameters', {})

                        print(f"[DEBUG] 🔧 执行步骤: {step_name} -> {action}")
                        
                        if action == "navigate":
                            url = parameters.get('url', '')
                            # Replace URL placeholder with actual task URL
                            if url == '{url}':
                                url = self.task.url
                            wait_until = parameters.get('waitUntil', 'load')

                            print(f"[DEBUG] 🌐 导航到: {url} (等待: {wait_until})")
                            await page.goto(url, wait_until=wait_until)
                            print(f"[DEBUG] ✅ 页面加载完成: {page.url}")
                            print(f"[DEBUG] 📄 页面标题: {await page.title()}")
                        elif action == "type":
                            text = parameters.get('text', '')
                            original_text = text
                            if text == '{username}':
                                text = username
                            elif text == '{password}':
                                text = password

                            selector = target.get('selector')
                            if selector:
                                print(f"[DEBUG] ⌨️  输入文本: {original_text} -> {text} (选择器: {selector})")
                                try:
                                    # Wait for element to be available with timeout
                                    print(f"[DEBUG] ⏳ 等待元素出现: {selector}")
                                    await page.wait_for_selector(selector, timeout=5000)
                                    print(f"[DEBUG] ✅ 元素已找到，开始输入")
                                    await page.fill(selector, text)
                                    print(f"[DEBUG] ✅ 文本输入完成")
                                except Exception as e:
                                    # Element not found, skip this step
                                    print(f"[DEBUG] ❌ 元素未找到: {selector} - {str(e)}")
                                    continue
                        elif action == "click":
                            selector = target.get('selector')
                            if selector:
                                print(f"[DEBUG] 🖱️  点击元素: {selector}")
                                try:
                                    # Wait for element to be clickable with timeout
                                    print(f"[DEBUG] ⏳ 等待元素可点击: {selector}")
                                    await page.wait_for_selector(selector, timeout=5000)
                                    print(f"[DEBUG] ✅ 元素已找到，开始点击")
                                    await page.click(selector)
                                    print(f"[DEBUG] ✅ 点击完成，等待页面响应")
                                    await page.wait_for_load_state('networkidle', timeout=10000)
                                    print(f"[DEBUG] ✅ 页面响应完成")
                                except Exception as e:
                                    # Element not found or click failed, skip this step
                                    print(f"[DEBUG] ❌ 点击失败: {selector} - {str(e)}")
                                    continue
                        elif action == "check":
                            # Check for success/failure indicators
                            success_selectors = step_config.get('success', [])
                            failure_selectors = step_config.get('failure', [])

                            print(f"[DEBUG] 🔍 检查结果 - 成功指示器: {len(success_selectors)} 个, 失败指示器: {len(failure_selectors)} 个")

                            # Check for success indicators
                            print(f"[DEBUG] ✅ 检查成功指示器...")
                            for i, success_selector in enumerate(success_selectors, 1):
                                print(f"[DEBUG]   {i}. 检查成功选择器: {success_selector}")
                                try:
                                    element = await page.wait_for_selector(success_selector, timeout=3000)
                                    if element:
                                        print(f"[DEBUG] 🎉 找到成功指示器: {success_selector}")
                                        return True
                                except Exception as e:
                                    print(f"[DEBUG]   ❌ 未找到: {success_selector}")
                                    continue

                            # Check for failure indicators
                            print(f"[DEBUG] ❌ 检查失败指示器...")
                            for i, failure_selector in enumerate(failure_selectors, 1):
                                print(f"[DEBUG]   {i}. 检查失败选择器: {failure_selector}")
                                try:
                                    element = await page.wait_for_selector(failure_selector, timeout=3000)
                                    if element:
                                        print(f"[DEBUG] 💥 找到失败指示器: {failure_selector}")
                                        return False
                                except Exception as e:
                                    print(f"[DEBUG]   ❌ 未找到: {failure_selector}")
                                    continue

                            print(f"[DEBUG] ❓ 未找到明确的成功或失败指示器，默认返回失败")
                    
                    print(f"[DEBUG] 📋 所有步骤执行完成，默认返回失败")
                    return False

                finally:
                    print(f"[DEBUG] 🔒 关闭浏览器")
                    await browser.close()

        except Exception as e:
            print(f"[DEBUG] 💥 执行异常: {str(e)}")
            self.log_message("ERROR", f"Attempt failed: {str(e)}", username, password)
            return False
    
    async def execute_task(self):
        """Execute the brute force task."""
        try:
            # Get task from database
            self.task = self.db.query(BruteForceTask).filter(BruteForceTask.id == self.task_id).first()
            if not self.task:
                raise Exception(f"Task {self.task_id} not found")
            
            self.log_message("INFO", f"Starting brute force task for URL: {self.task.url}")
            self.update_task_status(TaskStatus.RUNNING)
            
            # Check connectivity
            if not self.check_target_connectivity():
                self.update_task_status(TaskStatus.FAILED, "Target is not accessible")
                return
            
            # Load dictionaries
            usernames, passwords = self.load_dictionary_files(
                self.task.username_dict_path,
                self.task.password_dict_path
            )
            
            total_attempts = len(usernames) * len(passwords)
            self.update_progress(0, total_attempts)
            
            current_attempt = 0
            found_credentials = False
            
            # Execute brute force
            print(f"[INFO] 🚀 开始爆破测试，共 {len(usernames)} 个用户名 × {len(passwords)} 个密码 = {total_attempts} 次尝试")
            for username in usernames:
                if self.cancelled:
                    print(f"[INFO] 🚫 任务已取消，停止执行")
                    break

                print(f"[INFO] 👤 测试用户名: {username}")
                for password in passwords:
                    if self.cancelled:
                        print(f"[INFO] 🚫 任务已取消，停止执行")
                        break

                    current_attempt += 1
                    self.update_progress(current_attempt)

                    print(f"[INFO] 🔐 尝试登录 ({current_attempt}/{total_attempts}): {username} / {password}")
                    self.log_message("INFO", f"Attempting login ({current_attempt}/{total_attempts})", username, password)

                    if await self.run_single_attempt(self.task.config_template, username, password):
                        print(f"[SUCCESS] 🎉 发现有效凭据: {username} / {password}")
                        self.log_message("SUCCESS", f"Found valid credentials: {username}/{password}", username, password)
                        self.add_successful_result(username, password)
                        found_credentials = True
                        break
                    else:
                        print(f"[INFO] ❌ 登录失败: {username} / {password}")
                        self.log_message("INFO", f"Login failed", username, password)
                
                if found_credentials:
                    break
            
            # Update final status
            if self.cancelled:
                self.update_task_status(TaskStatus.CANCELLED)
                self.log_message("INFO", "Task was cancelled")
            elif found_credentials:
                self.update_task_status(TaskStatus.COMPLETED)
                self.log_message("SUCCESS", "Brute force completed successfully - credentials found")
            else:
                self.update_task_status(TaskStatus.COMPLETED)
                self.log_message("INFO", "Brute force completed - no valid credentials found")
                
        except Exception as e:
            self.log_message("ERROR", f"Task execution failed: {str(e)}")
            self.update_task_status(TaskStatus.FAILED, str(e))
    
    def cancel(self):
        """Cancel the task execution."""
        self.cancelled = True


class BruteForceService:
    """Independent brute force service that polls for tasks."""

    def __init__(self, poll_interval: int = 5):
        self.poll_interval = poll_interval
        self.running = False
        self.current_executor = None

    def get_pending_task(self) -> Optional[BruteForceTask]:
        """Get the next pending task from database."""
        db = SessionLocal()
        try:
            task = db.query(BruteForceTask).filter(
                BruteForceTask.status == TaskStatus.PENDING.value
            ).order_by(BruteForceTask.created_at).first()
            return task
        finally:
            db.close()

    async def run_service(self):
        """Main service loop."""
        print("🚀 Starting Brute Force Executor Service...")
        print(f"📊 Polling interval: {self.poll_interval} seconds")
        self.running = True

        while self.running:
            try:
                # Get next pending task
                task = self.get_pending_task()

                if task:
                    print(f"📋 Found pending task: {task.id} for URL: {task.url}")

                    # Execute task
                    self.current_executor = BruteForceExecutor(task.id)
                    await self.current_executor.execute_task()
                    self.current_executor = None

                    print(f"✅ Task {task.id} completed")
                else:
                    # No pending tasks, wait before next poll
                    await asyncio.sleep(self.poll_interval)

            except Exception as e:
                print(f"❌ Error in service loop: {e}")
                await asyncio.sleep(self.poll_interval)

    def stop_service(self):
        """Stop the service gracefully."""
        print("🛑 Stopping Brute Force Executor Service...")
        self.running = False

        if self.current_executor:
            print("⏹️  Cancelling current task...")
            self.current_executor.cancel()


# Global service instance
service = BruteForceService()


def signal_handler(signum, _):
    """Handle shutdown signals."""
    print(f"\n📡 Received signal {signum}")
    service.stop_service()


async def main():
    """Main entry point for the service."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        await service.run_service()
    except KeyboardInterrupt:
        print("\n👋 Service stopped by user")
    finally:
        print("🔚 Brute Force Executor Service stopped")


if __name__ == "__main__":
    asyncio.run(main())
