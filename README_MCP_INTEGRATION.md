# 🤖 Web爆破API的MCP集成

## 概述

将Web爆破API封装为MCP (Model Context Protocol) 服务器，使LLM能够直接通过MCP工具调用来提交爆破任务和监控进度。

## 🏗️ MCP架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     LLM     │───▶│ MCP Client  │───▶│ MCP Server  │
│  (Claude)   │    │             │    │(brute_force)│
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Database   │◀───│  Executor   │◀───│  Template   │
│  (SQLite)   │    │  Service    │    │  Storage    │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 🔧 MCP工具

### 1. submit_brute_force_task
提交URL和LLM生成的模板进行爆破测试。

**参数:**
- `url` (string): 目标URL
- `template` (string): LLM生成的YAML模板内容

**返回:**
```json
{
  "status": "success",
  "task_id": "uuid-string",
  "url": "http://target.com/login",
  "configuration": {
    "username_dict": "dicts/username.txt",
    "password_dict": "dicts/password.txt", 
    "config_template": "templates/llm-generated-uuid.yaml"
  },
  "task_status": "pending",
  "message": "Task submitted successfully. Template saved and ready for execution."
}
```

### 2. get_task_status
获取任务的详细状态、进度和日志。

**参数:**
- `task_id` (string): 任务ID

**返回:**
```json
{
  "status": "success",
  "task": {
    "id": "uuid-string",
    "url": "http://target.com/login",
    "status": "running",
    "created_at": "2024-01-01T00:00:00Z",
    "total_attempts": 100,
    "current_attempts": 50,
    "successful_attempts": 1,
    "found_credentials": true
  },
  "results": [...],
  "recent_logs": [...]
}
```

### 3. list_tasks
列出所有任务，支持状态过滤。

**参数:**
- `status` (string, optional): 状态过滤 (pending, running, completed, failed, cancelled)
- `limit` (int, optional): 返回数量限制 (默认: 10)
- `offset` (int, optional): 跳过数量 (默认: 0)

### 4. cancel_task
取消正在运行的任务。

**参数:**
- `task_id` (string): 要取消的任务ID

### 5. get_task_results
获取任务的成功结果（发现的凭据）。

**参数:**
- `task_id` (string): 任务ID

## 🚀 启动MCP服务器

### 方式1: 直接启动MCP服务器
```bash
python run_brute_force_mcp.py
```

### 方式2: 启动完整系统（MCP + 执行器）
```bash
# 启动MCP服务器
python run_brute_force_mcp.py &

# 启动执行器服务
python brute_force_executor.py &
```

### 方式3: 使用服务管理器
```bash
# 启动Web API + 执行器（然后可以单独启动MCP）
python start_services.py &
python run_brute_force_mcp.py
```

## 🔗 Claude Desktop集成

### 配置文件
在Claude Desktop的MCP配置中添加：

```json
{
  "mcpServers": {
    "brute-force-api": {
      "command": "python",
      "args": ["/absolute/path/to/py_brute_mcp/run_brute_force_mcp.py"],
      "env": {
        "PYTHONPATH": "/absolute/path/to/py_brute_mcp"
      }
    }
  }
}
```

### 使用示例
在Claude Desktop中，你可以这样使用：

```
请帮我对 http://target.com/login 进行爆破测试。

首先分析这个URL的登录表单，然后生成相应的执行模板，最后提交爆破任务。
```

Claude会：
1. 分析URL结构
2. 生成YAML模板
3. 调用 `submit_brute_force_task` MCP工具
4. 使用 `get_task_status` 监控进度
5. 使用 `get_task_results` 获取结果

## 🧪 测试MCP集成

### 基础测试
```bash
python test_brute_force_mcp.py
```

### 完整工作流程演示
```bash
python demo_mcp_workflow.py
```

## 📝 MCP工具使用示例

### Python MCP客户端
```python
import asyncio
import json
import subprocess

# 启动MCP服务器
server_process = subprocess.Popen(
    ["python", "run_brute_force_mcp.py"],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    text=True
)

# 调用MCP工具
async def call_mcp_tool(tool_name, arguments):
    request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        }
    }
    
    server_process.stdin.write(json.dumps(request) + "\n")
    server_process.stdin.flush()
    
    response = server_process.stdout.readline()
    return json.loads(response)

# 提交任务
template = "steps:\n  navigate:\n    action: navigate\n    ..."
response = await call_mcp_tool("submit_brute_force_task", {
    "url": "http://target.com/login",
    "template": template
})

result = json.loads(response["result"])
task_id = result["task_id"]

# 监控进度
response = await call_mcp_tool("get_task_status", {
    "task_id": task_id
})
```

## 🔄 完整工作流程

1. **LLM分析** → 分析目标URL，检测登录表单
2. **模板生成** → LLM生成YAML执行模板
3. **MCP调用** → 通过MCP工具提交URL和模板
4. **模板存储** → MCP服务器验证并保存模板
5. **任务入库** → 任务信息存储到数据库
6. **执行器轮询** → 执行器服务获取任务并执行
7. **进度监控** → 通过MCP工具实时查询进度
8. **结果获取** → 通过MCP工具获取发现的凭据

## 📊 MCP vs REST API对比

| 特性 | REST API | MCP |
|------|----------|-----|
| 调用方式 | HTTP请求 | MCP协议 |
| 集成复杂度 | 需要HTTP客户端 | 原生LLM集成 |
| 错误处理 | HTTP状态码 | 结构化JSON |
| 类型安全 | 依赖文档 | 工具定义 |
| LLM友好度 | 需要额外适配 | 原生支持 |

## 🎯 优势

### ✅ LLM原生集成
- 直接通过MCP协议调用
- 无需HTTP客户端代码
- 结构化的工具定义

### ✅ 类型安全
- 明确的参数定义
- 自动参数验证
- 结构化错误信息

### ✅ 开发友好
- 简单的工具调用接口
- 完整的错误处理
- 详细的返回信息

### ✅ 扩展性强
- 易于添加新工具
- 支持复杂参数类型
- 与现有系统无缝集成

## 📁 MCP相关文件

```
py_brute_mcp/
├── 🤖 MCP服务器
│   ├── brute_force_mcp_server.py  # MCP服务器主文件
│   └── run_brute_force_mcp.py     # MCP服务器启动脚本
├── 🧪 MCP测试
│   ├── test_brute_force_mcp.py    # MCP工具测试
│   └── demo_mcp_workflow.py       # 完整工作流程演示
├── ⚙️  配置文件
│   └── mcp_config_example.json    # Claude Desktop配置示例
└── 📚 文档
    └── README_MCP_INTEGRATION.md  # 本文档
```

## 🔧 故障排除

### 常见问题

1. **MCP服务器无法启动**
   - 检查依赖是否安装：`uv add fastmcp`
   - 确保字典文件存在

2. **工具调用失败**
   - 检查参数格式是否正确
   - 查看MCP服务器日志

3. **任务不执行**
   - 确保执行器服务正在运行
   - 检查数据库连接

4. **模板验证失败**
   - 检查YAML语法是否正确
   - 确保模板包含必要的步骤

## 💡 最佳实践

1. **模板设计**
   - 使用多个选择器提高成功率
   - 包含详细的成功/失败检测
   - 添加适当的等待时间

2. **错误处理**
   - 检查MCP工具返回的状态
   - 处理网络连接问题
   - 验证输入参数

3. **性能优化**
   - 合理设置字典大小
   - 使用批量查询减少数据库调用
   - 监控系统资源使用

---

🎯 **MCP集成完成！现在LLM可以通过标准MCP协议直接调用爆破功能，实现完全自动化的工作流程！**
