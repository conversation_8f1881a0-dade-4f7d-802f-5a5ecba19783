# Web Brute Force API System

一个基于FastAPI的Web爆破API系统，集成LLM生成的执行模板，提供URL提交和进度跟踪功能。

## 系统架构

系统采用分离式架构设计：

- **API Server** (`web_server.py`): 提供REST API接口，处理任务提交和查询
- **Executor Service** (`brute_force_executor.py`): 独立的执行器服务，轮询数据库获取任务并执行
- **Database** (`models.py`): SQLite数据库，存储任务、结果和日志

## 功能特性

- 🌐 RESTful API接口
- 📋 任务队列管理
- 📊 实时进度跟踪
- 🗄️ 结果持久化存储
- 📝 详细执行日志
- 🔄 异步任务执行
- ⚙️ 独立服务架构

## 快速开始

### 1. 安装依赖

```bash
# 确保已安装uv包管理器
uv add fastapi uvicorn sqlalchemy

# 安装Playwright浏览器
uv run python -m playwright install
```

### 2. 启动系统

```bash
# 方式1: 使用简单启动脚本（推荐）
python start.py

# 方式2: 使用服务管理器
python start_services.py

# 方式3: 手动启动各个服务
python -m uvicorn web_server:app --host 0.0.0.0 --port 8000 --reload  # API服务器
python brute_force_executor.py  # 执行器服务
```

### 3. 访问API

- **API服务器**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **交互式API**: http://localhost:8000/redoc

## API接口

### 提交任务

```bash
POST /api/tasks
```

**请求体:**
```json
{
  "url": "http://example.com/login",
  "template": "steps:\n  navigate:\n    action: navigate\n    parameters:\n      url: \"{url}\"\n  fill_username:\n    action: type\n    target:\n      selector: \"input[name='username']\"\n    parameters:\n      text: \"{username}\"\n  ..."
}
```

**响应:**
```json
{
  "task_id": "uuid-string",
  "url": "http://example.com/login",
  "configuration": {
    "username_dict": "dicts/username.txt",
    "password_dict": "dicts/password.txt",
    "config_template": "templates/llm-generated-uuid.yaml"
  },
  "status": "pending",
  "message": "Task submitted successfully. Template saved and ready for execution."
}
```

### 查询任务详情

```bash
GET /api/tasks/{task_id}
```

**响应:**
```json
{
  "task": {
    "id": "uuid-string",
    "url": "http://example.com/login",
    "status": "running",
    "created_at": "2024-01-01T00:00:00Z",
    "total_attempts": 100,
    "current_attempts": 50,
    "successful_attempts": 1,
    "found_credentials": true
  },
  "results": [
    {
      "username": "admin",
      "password": "password123",
      "discovered_at": "2024-01-01T00:30:00Z"
    }
  ],
  "recent_logs": [
    {
      "timestamp": "2024-01-01T00:30:00Z",
      "level": "SUCCESS",
      "message": "Found valid credentials: admin/password123",
      "username_attempted": "admin",
      "password_attempted": "password123"
    }
  ]
}
```

### 列出所有任务

```bash
GET /api/tasks?status=running&limit=10&offset=0
```

### 取消任务

```bash
DELETE /api/tasks/{task_id}
```

## 任务状态

- `pending`: 等待执行
- `running`: 正在执行
- `completed`: 执行完成
- `failed`: 执行失败
- `cancelled`: 已取消

## 配置文件

### 字典文件

- `dicts/username.txt`: 用户名字典
- `dicts/password.txt`: 密码字典

### 模板文件

- `templates/test-grafana.yaml`: Grafana登录模板
- `templates/test-dvwa.yaml`: DVWA登录模板

## 使用示例

### Python客户端示例

```python
import requests
import time

# LLM生成的模板示例
template = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load

  fill_username:
    action: type
    target:
      selector: "input[name='username']"
    parameters:
      text: "{username}"

  fill_password:
    action: type
    target:
      selector: "input[name='password']"
    parameters:
      text: "{password}"

  submit_form:
    action: click
    target:
      selector: "button[type='submit']"

  check_result:
    action: check
    success:
      - ".dashboard"
    failure:
      - ".error"
"""

# 提交任务 - URL + LLM生成的模板
response = requests.post("http://localhost:8000/api/tasks", json={
    "url": "http://localhost:3000/login",
    "template": template
})

result = response.json()
task_id = result["task_id"]
print(f"Task submitted: {task_id}")
print(f"Template saved to: {result['configuration']['config_template']}")

# 轮询任务状态
while True:
    response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
    task_data = response.json()

    status = task_data["task"]["status"]
    progress = f"{task_data['task']['current_attempts']}/{task_data['task']['total_attempts']}"

    print(f"Status: {status}, Progress: {progress}")

    if status in ["completed", "failed", "cancelled"]:
        break

    time.sleep(5)

# 查看结果
if task_data["task"]["found_credentials"]:
    print("Found credentials:")
    for result in task_data["results"]:
        print(f"  {result['username']}:{result['password']}")
```

### curl示例

```bash
# 提交任务 - 简化版本，只需要URL
curl -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d '{"url": "http://localhost:3000/login"}'

# 查询任务
curl "http://localhost:8000/api/tasks/your-task-id"

# 列出所有任务
curl "http://localhost:8000/api/tasks"
```

## 项目结构

```
py_brute_mcp/
├── models.py                   # 数据库模型
├── web_server.py              # API服务器
├── brute_force_executor.py    # 执行器服务
├── start_services.py          # 服务管理器
├── start.py                   # 简单启动脚本
├── dicts/                     # 字典文件目录
│   ├── username.txt
│   └── password.txt
├── templates/                 # 配置模板目录
│   ├── test-grafana.yaml
│   └── test-dvwa.yaml
└── brute_force.db            # SQLite数据库文件
```

## 注意事项

1. 确保目标服务器可访问
2. 字典文件格式为每行一个条目
3. 配置模板需要正确配置选择器
4. 数据库文件会自动创建
5. 执行器服务必须运行才能处理任务

## 故障排除

### 常见问题

1. **任务一直处于pending状态**
   - 检查执行器服务是否运行
   - 查看执行器服务日志

2. **连接超时**
   - 检查目标URL是否可访问
   - 检查网络连接

3. **找不到字典文件**
   - 确保字典文件路径正确
   - 检查文件权限

4. **数据库错误**
   - 删除`brute_force.db`文件重新初始化
   - 检查磁盘空间

## 开发和扩展

系统采用模块化设计，易于扩展：

- 添加新的爆破模板
- 支持更多认证方式
- 集成其他数据库
- 添加Web界面
- 支持分布式执行
