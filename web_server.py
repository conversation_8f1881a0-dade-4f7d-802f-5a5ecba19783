#!/usr/bin/env python3
"""
Web API server for brute force task management.
Provides REST API endpoints for submitting URLs and tracking progress.
"""

import asyncio
import os
import uuid
import yaml
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel, HttpUrl
from sqlalchemy.orm import Session
from sqlalchemy import desc

from models import (
    get_db, init_database, BruteForceTask, BruteForceResult, TaskLog, TaskStatus
)


# Pydantic models for API
class TaskSubmissionRequest(BaseModel):
    """Request model for task submission."""
    url: HttpUrl
    template: str  # YAML template content generated by LLM


class TaskResponse(BaseModel):
    """Response model for task information."""
    id: str
    url: str
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_attempts: int
    current_attempts: int
    successful_attempts: int
    found_credentials: bool
    error_message: Optional[str] = None


class TaskResultResponse(BaseModel):
    """Response model for task results."""
    username: str
    password: str
    discovered_at: datetime
    response_details: Optional[str] = None


class TaskLogResponse(BaseModel):
    """Response model for task logs."""
    timestamp: datetime
    level: str
    message: str
    username_attempted: Optional[str] = None
    password_attempted: Optional[str] = None


class TaskDetailResponse(BaseModel):
    """Detailed response model for task with results and logs."""
    task: TaskResponse
    results: List[TaskResultResponse]
    recent_logs: List[TaskLogResponse]


# Initialize FastAPI app
app = FastAPI(
    title="Web Brute Force API",
    description="API for submitting URLs and tracking web brute force progress",
    version="1.0.0"
)


def ensure_templates_directory():
    """Ensure templates directory exists."""
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    return templates_dir


def save_template_to_file(template_content: str) -> str:
    """Save template content to a file and return the file path."""
    templates_dir = ensure_templates_directory()

    # Generate unique filename
    template_id = str(uuid.uuid4())
    filename = f"llm-generated-{template_id}.yaml"
    filepath = os.path.join(templates_dir, filename)

    try:
        # Validate YAML format
        yaml.safe_load(template_content)

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(template_content)

        print(f"📝 Template saved: {filepath}")
        return filepath

    except yaml.YAMLError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid YAML template: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to save template: {str(e)}"
        )


@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    init_database()
    ensure_templates_directory()


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Web Brute Force API Server",
        "version": "3.0.0",
        "description": "Submit URLs with LLM-generated templates for automated brute force testing.",
        "endpoints": {
            "submit_task": "POST /api/tasks - Submit URL and LLM-generated template",
            "get_task": "GET /api/tasks/{task_id} - Get task details and progress",
            "list_tasks": "GET /api/tasks - List all tasks with optional filtering",
            "cancel_task": "DELETE /api/tasks/{task_id} - Cancel a running task"
        },
        "usage": {
            "submit": "POST /api/tasks with JSON: {\"url\": \"http://target.com/login\", \"template\": \"yaml_content\"}",
            "monitor": "GET /api/tasks/{task_id} to track progress",
            "note": "Templates are generated by LLM and stored locally. Dictionaries are automatically selected."
        },
        "workflow": {
            "1": "LLM analyzes URL and detects login forms",
            "2": "LLM generates YAML execution template",
            "3": "API receives URL + template and stores template locally",
            "4": "Executor service polls database and executes tasks"
        }
    }


@app.post("/api/tasks")
async def submit_task(
    request: TaskSubmissionRequest,
    db: Session = Depends(get_db)
):
    """
    Submit a new brute force task with URL and LLM-generated template.

    Args:
        request: Task submission request with URL and template content
        db: Database session

    Returns:
        Dictionary with task ID and configuration used
    """
    # Use default dictionary paths
    default_username_dict = "dicts/username.txt"
    default_password_dict = "dicts/password.txt"

    # Validate default dictionary files exist
    if not os.path.exists(default_username_dict):
        raise HTTPException(
            status_code=500,
            detail=f"Default username dictionary not found: {default_username_dict}"
        )

    if not os.path.exists(default_password_dict):
        raise HTTPException(
            status_code=500,
            detail=f"Default password dictionary not found: {default_password_dict}"
        )

    # Save the LLM-generated template to file
    template_filepath = save_template_to_file(request.template)

    # Create new task with LLM-generated template
    task = BruteForceTask(
        url=str(request.url),
        username_dict_path=default_username_dict,
        password_dict_path=default_password_dict,
        config_template=template_filepath,
        status=TaskStatus.PENDING.value
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)

    # Task will be picked up by the executor service
    print(f"📋 New task created: {task.id} for URL: {task.url}")

    return {
        "task_id": task.id,
        "url": task.url,
        "configuration": {
            "username_dict": default_username_dict,
            "password_dict": default_password_dict,
            "config_template": template_filepath
        },
        "status": "pending",
        "message": "Task submitted successfully. Template saved and ready for execution."
    }


@app.get("/api/tasks/{task_id}", response_model=TaskDetailResponse)
async def get_task(task_id: str, db: Session = Depends(get_db)):
    """
    Get detailed information about a specific task.
    
    Args:
        task_id: Task ID to retrieve
        db: Database session
        
    Returns:
        Detailed task information with results and logs
    """
    task = db.query(BruteForceTask).filter(BruteForceTask.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    # Get results
    results = [
        TaskResultResponse(
            username=result.username,
            password=result.password,
            discovered_at=result.discovered_at,
            response_details=result.response_details
        )
        for result in task.results
    ]
    
    # Get recent logs (last 50)
    recent_logs = [
        TaskLogResponse(
            timestamp=log.timestamp,
            level=log.level,
            message=log.message,
            username_attempted=log.username_attempted,
            password_attempted=log.password_attempted
        )
        for log in db.query(TaskLog)
        .filter(TaskLog.task_id == task_id)
        .order_by(desc(TaskLog.timestamp))
        .limit(50)
        .all()
    ]
    
    task_response = TaskResponse(
        id=task.id,
        url=task.url,
        status=task.status,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at,
        total_attempts=task.total_attempts,
        current_attempts=task.current_attempts,
        successful_attempts=task.successful_attempts,
        found_credentials=task.found_credentials,
        error_message=task.error_message
    )
    
    return TaskDetailResponse(
        task=task_response,
        results=results,
        recent_logs=recent_logs
    )


@app.get("/api/tasks", response_model=List[TaskResponse])
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    List all tasks with optional filtering.
    
    Args:
        status: Optional status filter
        limit: Maximum number of tasks to return
        offset: Number of tasks to skip
        db: Database session
        
    Returns:
        List of tasks
    """
    query = db.query(BruteForceTask)
    
    if status:
        query = query.filter(BruteForceTask.status == status)
    
    tasks = query.order_by(desc(BruteForceTask.created_at)).offset(offset).limit(limit).all()
    
    return [
        TaskResponse(
            id=task.id,
            url=task.url,
            status=task.status,
            created_at=task.created_at,
            started_at=task.started_at,
            completed_at=task.completed_at,
            total_attempts=task.total_attempts,
            current_attempts=task.current_attempts,
            successful_attempts=task.successful_attempts,
            found_credentials=task.found_credentials,
            error_message=task.error_message
        )
        for task in tasks
    ]


@app.delete("/api/tasks/{task_id}")
async def cancel_task(task_id: str, db: Session = Depends(get_db)):
    """
    Cancel a running task.
    
    Args:
        task_id: Task ID to cancel
        db: Database session
        
    Returns:
        Success message
    """
    task = db.query(BruteForceTask).filter(BruteForceTask.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    if task.status in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]:
        raise HTTPException(status_code=400, detail=f"Cannot cancel task with status: {task.status}")
    
    task.status = TaskStatus.CANCELLED.value
    task.completed_at = datetime.now(timezone.utc)
    db.commit()
    
    return {"message": f"Task {task_id} cancelled successfully"}


# Note: Task execution is now handled by the independent executor service
# No background task execution in the API server


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
