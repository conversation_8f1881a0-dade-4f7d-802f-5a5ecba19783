# Playwright 自动化测试模板语法手册

## 概述

本手册详细介绍了基于 Playwright 的自动化测试模板的 YAML 语法规范。通过这个模板系统，您可以创建可重用的自动化测试流程，用于登录测试、表单填写、页面交互等场景。

## 模板基本结构

```yaml
# 模板元信息
name: "模板名称"
description: "模板描述"

# 变量定义
variables:
  - variable_name1
  - variable_name2

# 成功/失败条件
success_condition:
  # 成功条件定义
failure_condition:
  # 失败条件定义

# 执行步骤
steps:
  - name: "步骤名称"
    action: "动作类型"
    target:
      # 目标元素定义
    parameters:
      # 参数定义
    description: "步骤描述"
```

## 1. 模板元信息

### 基本属性
```yaml
name: "模板名称"                    # 必需：模板的显示名称
description: "模板功能描述"          # 可选：模板的详细描述
```

**示例：**
```yaml
name: "MinIO Console Login Template"
description: "A reusable flow for attempting to log into a MinIO Console instance."
```

## 2. 变量系统

### 变量定义
```yaml
variables:
  - username    # 用户名变量
  - password    # 密码变量
  - custom_var  # 自定义变量
```

### 变量使用
在 `parameters` 中使用 `{变量名}` 格式引用变量：
```yaml
parameters:
  text: "{username}"
  url: "http://example.com/{custom_var}"
```

**支持的内置变量：**
- `{username}` - 从用户名字典文件读取
- `{password}` - 从密码字典文件读取

## 3. 条件判断系统

### 成功条件 (success_condition)
定义登录成功的判断标准：
```yaml
success_condition:
  any:  # 满足任一条件即为成功
    - type: element_is_visible
      target:
        type: text
        value: "Welcome"
    - type: url_contains
      value: "/dashboard"
```

### 失败条件 (failure_condition)
定义登录失败的判断标准：
```yaml
failure_condition:
  any:  # 满足任一条件即为失败
    - type: element_is_visible
      target:
        type: text
        value: "Login failed"
```

### 条件类型

#### element_is_visible
检查指定元素是否可见：
```yaml
- type: element_is_visible
  target:
    type: text
    value: "错误消息文本"
```

#### url_contains
检查当前URL是否包含指定字符串：
```yaml
- type: url_contains
  value: "/success"
```

## 4. 执行步骤 (steps)

### 步骤基本结构
```yaml
- name: "步骤名称"              # 必需：步骤的显示名称
  action: "动作类型"            # 必需：要执行的动作
  target:                      # 可选：目标元素（某些动作需要）
    type: "定位器类型"
    value: "定位器值"
    options: {}               # 可选：定位器选项
  parameters:                  # 可选：动作参数
    key: "value"
  description: "步骤描述"       # 可选：步骤的详细描述
```

### 支持的动作类型 (action)

#### navigate - 页面导航
导航到指定URL：
```yaml
- name: "Navigate to Login Page"
  action: "navigate"
  parameters:
    url: "http://localhost:3000/login"
    waitUntil: "networkidle"  # 可选：load, domcontentloaded, networkidle
  description: "打开登录页面"
```

**参数说明：**
- `url`: 目标URL（必需）
- `waitUntil`: 等待条件（可选）
  - `load`: 等待页面完全加载
  - `domcontentloaded`: 等待DOM加载完成
  - `networkidle`: 等待网络空闲

#### type - 文本输入
在指定元素中输入文本：
```yaml
- name: "Fill Username"
  action: "type"
  target:
    type: "role"
    value: "textbox"
    options:
      name: "Username"
  parameters:
    text: "{username}"
  description: "填写用户名"
```

**参数说明：**
- `text`: 要输入的文本（必需）

#### click - 点击操作
点击指定元素：
```yaml
- name: "Click Login Button"
  action: "click"
  target:
    type: "role"
    value: "button"
    options:
      name: "Login"
  description: "点击登录按钮"
```

#### wait - 等待操作
等待指定时间：
```yaml
- name: "Wait for Response"
  action: "wait"
  parameters:
    time: 2  # 等待秒数
  description: "等待响应"
```

**参数说明：**
- `time`: 等待时间（秒）

## 5. 目标元素定位 (target)

### 定位器类型

#### role - 基于角色的定位器（推荐）
使用 ARIA 角色定位元素：
```yaml
target:
  type: "role"
  value: "textbox"        # 角色类型
  options:
    name: "Username"      # 可访问名称
    exact: true          # 精确匹配（可选）
```

**常用角色类型：**
- `textbox` - 文本输入框
- `button` - 按钮
- `link` - 链接
- `checkbox` - 复选框
- `radio` - 单选按钮
- `combobox` - 下拉框

**选项参数：**
- `name`: 元素的可访问名称
- `exact`: 是否精确匹配名称
- `disabled`: 是否匹配禁用状态
- `expanded`: 是否匹配展开状态

#### selector - CSS选择器定位器
使用 CSS 选择器定位元素：
```yaml
target:
  type: "selector"
  value: "input[name='username']"
```

**示例选择器：**
- `#id` - ID选择器
- `.class` - 类选择器
- `input[name='username']` - 属性选择器
- `form > input` - 子元素选择器

#### text - 文本内容定位器
基于文本内容定位元素：
```yaml
target:
  type: "text"
  value: "Login"
```

## 6. 完整示例

### 基础登录模板
```yaml
name: "Generic Login Template"
description: "通用登录模板"

variables:
  - username
  - password

failure_condition:
  any:
    - type: element_is_visible
      target:
        type: text
        value: "Invalid credentials"

steps:
  - name: "Navigate to Login"
    action: "navigate"
    parameters:
      url: "http://localhost:8080/login"
      waitUntil: "networkidle"
    description: "打开登录页面"

  - name: "Enter Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "输入用户名"

  - name: "Enter Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "输入密码"

  - name: "Submit Form"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "提交登录表单"

  - name: "Wait for Result"
    action: "wait"
    parameters:
      time: 2
    description: "等待登录结果"
```

## 7. 最佳实践

### 1. 定位器选择优先级
1. **role** - 最推荐，基于语义，稳定性高
2. **text** - 适用于按钮、链接等有明确文本的元素
3. **selector** - 最后选择，维护成本高

### 2. 等待策略
- 页面导航后使用 `networkidle`
- 表单提交后添加适当的等待时间
- 避免使用过长的固定等待时间

### 3. 错误处理
- 明确定义失败条件
- 使用具体的错误消息文本
- 考虑多种可能的失败场景

### 4. 变量命名
- 使用有意义的变量名
- 保持命名一致性
- 避免使用特殊字符

## 8. 扩展功能

### 条件执行（计划中）
```yaml
- name: "Conditional Step"
  action: "click"
  condition: "{login_method} == 'oauth'"
  target:
    type: "text"
    value: "Login with OAuth"
```

### 循环操作（计划中）
```yaml
- name: "Fill Multiple Fields"
  action: "loop"
  items: "{form_fields}"
  steps:
    - action: "type"
      target:
        type: "selector"
        value: "input[name='{item.name}']"
      parameters:
        text: "{item.value}"
```

## 9. 故障排除

### 常见错误
1. **元素定位失败** - 检查定位器是否正确
2. **超时错误** - 增加等待时间或改变等待策略
3. **变量替换失败** - 确认变量名拼写正确

### 调试技巧
1. 启用非无头模式查看执行过程
2. 使用浏览器开发者工具验证选择器
3. 检查网络请求和响应

## 10. 高级用法

### 复杂表单处理
```yaml
# 处理多步骤表单
steps:
  - name: "Fill Step 1"
    action: "type"
    target:
      type: "selector"
      value: "#step1-field"
    parameters:
      text: "{username}"

  - name: "Next Step"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Next"

  - name: "Fill Step 2"
    action: "type"
    target:
      type: "selector"
      value: "#step2-field"
    parameters:
      text: "{password}"
```

### 处理动态内容
```yaml
# 等待动态加载的元素
- name: "Wait for Dynamic Content"
  action: "wait"
  parameters:
    time: 3
  description: "等待动态内容加载"

- name: "Interact with Dynamic Element"
  action: "click"
  target:
    type: "selector"
    value: ".dynamic-button:visible"
```

### 多种登录方式支持
```yaml
# 支持不同的登录入口
steps:
  - name: "Choose Login Method"
    action: "click"
    target:
      type: "text"
      value: "Standard Login"
    description: "选择标准登录方式"

  - name: "Fill Credentials"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Email"
    parameters:
      text: "{username}"
```

## 11. 性能优化

### 减少等待时间
```yaml
# 使用精确的等待条件而不是固定时间
- name: "Navigate with Optimal Wait"
  action: "navigate"
  parameters:
    url: "http://example.com"
    waitUntil: "domcontentloaded"  # 比 networkidle 更快
```

### 批量操作
```yaml
# 将相关操作组合在一起
- name: "Fill All Fields"
  action: "type"
  target:
    type: "selector"
    value: "input[name='username']"
  parameters:
    text: "{username}"

- name: "Fill Password Immediately"
  action: "type"
  target:
    type: "selector"
    value: "input[name='password']"
  parameters:
    text: "{password}"
```

## 12. 安全考虑

### 敏感信息处理
- 避免在模板中硬编码敏感信息
- 使用变量系统管理凭据
- 定期清理日志文件

### 访问控制
- 仅在授权环境中使用
- 控制模板文件的访问权限
- 监控自动化测试的执行

## 13. 版本兼容性

当前版本支持的功能：
- ✅ 基本动作：navigate, type, click, wait
- ✅ 定位器：role, selector, text
- ✅ 条件判断：element_is_visible
- ✅ 变量系统：username, password
- ⏳ 计划功能：条件执行、循环操作、更多定位器类型

## 14. 附录

### A. 常用角色类型参考
| 角色类型 | 描述 | 示例 |
|---------|------|------|
| textbox | 文本输入框 | `<input type="text">` |
| button | 按钮 | `<button>`, `<input type="submit">` |
| link | 链接 | `<a href="...">` |
| checkbox | 复选框 | `<input type="checkbox">` |
| radio | 单选按钮 | `<input type="radio">` |
| combobox | 下拉框 | `<select>` |
| listbox | 列表框 | `<select multiple>` |
| tab | 标签页 | `<div role="tab">` |
| dialog | 对话框 | `<div role="dialog">` |

### B. CSS选择器参考
| 选择器类型 | 语法 | 示例 |
|-----------|------|------|
| ID选择器 | `#id` | `#username` |
| 类选择器 | `.class` | `.login-form` |
| 属性选择器 | `[attr="value"]` | `[name="username"]` |
| 子元素选择器 | `parent > child` | `form > input` |
| 后代选择器 | `ancestor descendant` | `div input` |
| 伪类选择器 | `:pseudo-class` | `:visible`, `:enabled` |

### C. 错误代码参考
| 错误类型 | 描述 | 解决方案 |
|---------|------|---------|
| TimeoutError | 元素定位超时 | 检查定位器，增加等待时间 |
| ElementNotFound | 元素未找到 | 验证选择器正确性 |
| NetworkError | 网络连接错误 | 检查目标服务可用性 |
| ValidationError | 模板格式错误 | 检查YAML语法 |
