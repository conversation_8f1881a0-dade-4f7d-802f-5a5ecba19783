#!/usr/bin/env python3
"""
Complete MCP workflow demonstration.
Shows LLM → MCP → Executor → Results workflow.
"""

import json
import subprocess
import sys
import time
from pathlib import Path


class MCPWorkflowDemo:
    """Complete MCP workflow demonstration."""
    
    def __init__(self):
        self.server_process = None
    
    def start_mcp_server(self):
        """Start the MCP server."""
        server_script = Path(__file__).parent / "brute_force_mcp_server.py"
        print(f"🚀 Starting MCP server...")
        
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        time.sleep(3)
        print("✅ MCP server started")
    
    def initialize_mcp(self):
        """Initialize MCP connection."""
        print("🔧 Initializing MCP connection...")
        
        # Send initialization request
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "demo-client", "version": "1.0.0"}
            }
        }
        
        self.server_process.stdin.write(json.dumps(init_request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            server_name = response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')
            print(f"✅ Connected to MCP server: {server_name}")
        
        # Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        self.server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        self.server_process.stdin.flush()
    
    def call_mcp_tool(self, tool_name: str, arguments: dict, request_id: int):
        """Call an MCP tool."""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                return json.loads(response["result"]["content"][0]["text"])
            elif "error" in response:
                return {"status": "error", "error": response["error"]["message"]}
        
        return {"status": "error", "error": "No response"}
    
    def simulate_llm_workflow(self):
        """Simulate the complete LLM workflow."""
        print("\n🤖 Simulating LLM Workflow")
        print("=" * 50)
        
        # Simulate LLM analyzing different types of URLs
        test_cases = [
            {
                "url": "http://httpbin.org/status/200",
                "analysis": "Simple HTTP endpoint - basic connectivity test",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  check_result:
    action: check
    success:
      - "body"
    failure:
      - ".error"
"""
            },
            {
                "url": "https://httpbin.org/basic-auth/user/pass",
                "analysis": "Basic auth endpoint - requires authentication",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='text']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password']"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit']"
  
  check_result:
    action: check
    success:
      - ".authenticated"
      - "text=authenticated"
    failure:
      - ".error"
      - "text=Unauthorized"
"""
            }
        ]
        
        submitted_tasks = []
        request_id = 2
        
        for case in test_cases:
            print(f"\n📋 Processing: {case['url']}")
            print(f"🤖 [LLM Analysis] {case['analysis']}")
            print(f"🤖 [LLM] Generating execution template...")
            
            # Submit via MCP
            print(f"📡 [MCP] Submitting task...")
            result = self.call_mcp_tool("submit_brute_force_task", {
                "url": case["url"],
                "template": case["template"]
            }, request_id)
            
            request_id += 1
            
            if result["status"] == "success":
                task_id = result["task_id"]
                print(f"✅ [MCP] Task submitted: {task_id}")
                print(f"📝 [MCP] Template: {result['configuration']['config_template']}")
                submitted_tasks.append(task_id)
            else:
                print(f"❌ [MCP] Task submission failed: {result['error']}")
        
        return submitted_tasks, request_id
    
    def monitor_tasks(self, task_ids: list, start_request_id: int):
        """Monitor submitted tasks."""
        print(f"\n📊 Monitoring {len(task_ids)} tasks...")
        
        request_id = start_request_id
        
        for task_id in task_ids:
            print(f"\n🔍 [MCP] Checking task: {task_id[:8]}...")
            
            result = self.call_mcp_tool("get_task_status", {
                "task_id": task_id
            }, request_id)
            
            request_id += 1
            
            if result["status"] == "success":
                task = result["task"]
                print(f"  📈 Status: {task['status']}")
                print(f"  📊 Progress: {task['current_attempts']}/{task['total_attempts']}")
                print(f"  🎯 Found credentials: {task['found_credentials']}")
                
                if task['error_message']:
                    print(f"  ❌ Error: {task['error_message']}")
                
                # Show recent logs
                if result.get("recent_logs"):
                    print(f"  📝 Recent logs:")
                    for log in result["recent_logs"][-2:]:
                        print(f"    [{log['level']}] {log['message']}")
            else:
                print(f"  ❌ Error: {result['error']}")
    
    def list_all_tasks(self, request_id: int):
        """List all tasks via MCP."""
        print(f"\n📋 [MCP] Listing all tasks...")
        
        result = self.call_mcp_tool("list_tasks", {"limit": 10}, request_id)
        
        if result["status"] == "success":
            tasks = result["tasks"]
            print(f"✅ [MCP] Found {len(tasks)} tasks")
            
            for i, task in enumerate(tasks[:5], 1):
                status_emoji = {
                    "pending": "⏳",
                    "running": "🔄", 
                    "completed": "✅",
                    "failed": "❌",
                    "cancelled": "🚫"
                }.get(task['status'], "❓")
                
                print(f"  {i}. {status_emoji} {task['id'][:8]}... - {task['status']} - {task['url']}")
        else:
            print(f"❌ [MCP] Error listing tasks: {result['error']}")
    
    def stop_server(self):
        """Stop the MCP server."""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 MCP server stopped")
    
    def run_demo(self):
        """Run the complete demo."""
        print("🎯 Complete MCP Workflow Demonstration")
        print("=" * 60)
        print("Simulating: LLM → MCP → Executor → Results")
        print("=" * 60)
        
        try:
            # Start MCP server
            self.start_mcp_server()
            
            # Initialize MCP connection
            self.initialize_mcp()
            
            # Simulate LLM workflow
            submitted_tasks, next_request_id = self.simulate_llm_workflow()
            
            # Wait for executor to process
            if submitted_tasks:
                print(f"\n⏳ Waiting 10 seconds for executor to process tasks...")
                time.sleep(10)
                
                # Monitor tasks
                self.monitor_tasks(submitted_tasks, next_request_id)
                next_request_id += len(submitted_tasks)
            
            # List all tasks
            self.list_all_tasks(next_request_id)
            
            print("\n" + "=" * 60)
            print("🎉 Complete MCP Workflow Demo Finished!")
            print("\n📚 What was demonstrated:")
            print("  ✅ MCP server initialization and tool discovery")
            print("  ✅ LLM template generation simulation")
            print("  ✅ Task submission via MCP tools")
            print("  ✅ Template validation and storage")
            print("  ✅ Task monitoring and progress tracking")
            print("  ✅ Integration with executor service")
            
            print(f"\n🔗 MCP Integration Points:")
            print(f"  • Claude Desktop: Add to MCP configuration")
            print(f"  • Custom clients: Use JSON-RPC over STDIO")
            print(f"  • LLM applications: Direct tool calling")
            
        finally:
            self.stop_server()


def main():
    """Main entry point."""
    demo = MCPWorkflowDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
