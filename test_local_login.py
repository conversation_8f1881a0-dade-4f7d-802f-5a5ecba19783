#!/usr/bin/env python3
"""
测试本地登录页面 http://127.0.0.1:9001/login 的爆破功能。
验证执行器的循环执行机制。
"""

import json
import subprocess
import sys
import time
from pathlib import Path


class LocalLoginTest:
    """本地登录测试类。"""
    
    def __init__(self):
        self.server_process = None
        self.target_url = "http://127.0.0.1:9001/login"
    
    def start_mcp_server(self):
        """启动MCP服务器。"""
        server_script = Path(__file__).parent / "brute_force_mcp_server.py"
        print(f"🚀 启动MCP服务器...")
        
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        time.sleep(3)
        print("✅ MCP服务器已启动")
    
    def initialize_mcp(self):
        """初始化MCP连接。"""
        print("🔧 初始化MCP连接...")
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "local-login-test", "version": "1.0.0"}
            }
        }
        
        self.server_process.stdin.write(json.dumps(init_request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            server_name = response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')
            print(f"✅ 已连接到MCP服务器: {server_name}")
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        self.server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        self.server_process.stdin.flush()
    
    def submit_task(self, template: str, request_id: int) -> str:
        """提交任务到本地登录页面。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "submit_brute_force_task",
                "arguments": {
                    "url": self.target_url,
                    "template": template
                }
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                result = json.loads(response["result"]["content"][0]["text"])
                if result["status"] == "success":
                    return result["task_id"]
        
        return None
    
    def get_task_status(self, task_id: str, request_id: int) -> dict:
        """获取任务状态。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "get_task_status",
                "arguments": {"task_id": task_id}
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                return json.loads(response["result"]["content"][0]["text"])
        
        return {"status": "error", "error": "No response"}
    
    def generate_login_template(self) -> str:
        """为本地登录页面生成模板。"""
        return """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[name='user'], input[type='text'], #username, #user"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password, #pass"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login, .btn-login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - ".home"
      - ".success"
      - "text=Welcome"
      - "text=Dashboard"
      - "text=Success"
    failure:
      - ".error"
      - ".alert-danger"
      - ".login-error"
      - "text=Invalid"
      - "text=Login failed"
      - "text=Error"
"""
    
    def test_local_login(self):
        """测试本地登录页面。"""
        print(f"\n🎯 测试本地登录页面")
        print("=" * 60)
        print(f"目标URL: {self.target_url}")
        print("=" * 60)
        
        # 生成登录模板
        template = self.generate_login_template()
        print("📝 生成登录模板:")
        print("   - 支持多种用户名/密码字段选择器")
        print("   - 支持多种提交按钮选择器")
        print("   - 支持多种成功/失败检测")
        
        # 提交任务
        print(f"\n📤 提交爆破任务...")
        task_id = self.submit_task(template, 2)
        
        if task_id:
            print(f"✅ 任务已提交: {task_id}")
            print(f"🔄 执行器将开始处理任务...")
            
            # 监控任务执行
            self.monitor_task(task_id)
        else:
            print(f"❌ 任务提交失败")
    
    def monitor_task(self, task_id: str):
        """监控任务执行。"""
        print(f"\n📈 监控任务执行进度...")
        print("=" * 60)
        
        request_id = 3
        monitoring_rounds = 0
        max_rounds = 30  # 最多监控30轮 (5分钟)
        
        while monitoring_rounds < max_rounds:
            monitoring_rounds += 1
            
            status_result = self.get_task_status(task_id, request_id)
            request_id += 1
            
            if status_result["status"] == "success":
                task_data = status_result["task"]
                status = task_data["status"]
                progress = f"{task_data['current_attempts']}/{task_data['total_attempts']}"
                found_credentials = task_data.get("found_credentials", False)
                
                status_emoji = {
                    "pending": "⏳",
                    "running": "🔄",
                    "completed": "✅",
                    "failed": "❌",
                    "cancelled": "🚫"
                }.get(status, "❓")
                
                print(f"第 {monitoring_rounds:2d} 轮 | {status_emoji} {status:<10} | 进度: {progress:<8} | 发现凭据: {'是' if found_credentials else '否'}")
                
                # 显示最近的日志
                if status_result.get("recent_logs"):
                    latest_logs = status_result["recent_logs"][-2:]  # 显示最近2条日志
                    for log in latest_logs:
                        if log["level"] in ["SUCCESS", "ERROR"]:
                            level_emoji = "🎯" if log["level"] == "SUCCESS" else "❌"
                            print(f"         {level_emoji} {log['message']}")
                
                # 任务完成
                if status in ["completed", "failed", "cancelled"]:
                    print(f"\n🏁 任务执行完成!")
                    print(f"📊 最终状态: {status}")
                    print(f"📈 总尝试次数: {task_data['current_attempts']}")
                    print(f"🎯 发现凭据: {'是' if found_credentials else '否'}")
                    
                    if task_data.get("error_message"):
                        print(f"❌ 错误信息: {task_data['error_message']}")
                    
                    # 如果发现凭据，获取详细结果
                    if found_credentials:
                        self.get_task_results(task_id, request_id)
                    
                    break
            else:
                print(f"第 {monitoring_rounds:2d} 轮 | ❌ 查询失败: {status_result.get('error', 'Unknown error')}")
            
            # 等待10秒后继续监控
            if monitoring_rounds < max_rounds:
                time.sleep(10)
        
        if monitoring_rounds >= max_rounds:
            print(f"\n⏰ 监控超时 (已监控 {max_rounds} 轮)")
    
    def get_task_results(self, task_id: str, request_id: int):
        """获取任务结果。"""
        print(f"\n🎯 获取发现的凭据...")
        
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "get_task_results",
                "arguments": {"task_id": task_id}
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                result = json.loads(response["result"]["content"][0]["text"])
                if result["status"] == "success" and result["results"]:
                    print(f"🎉 发现有效凭据:")
                    for i, cred in enumerate(result["results"], 1):
                        print(f"  {i}. 用户名: {cred['username']}")
                        print(f"     密码: {cred['password']}")
                        print(f"     发现时间: {cred['discovered_at']}")
                else:
                    print(f"📝 未发现有效凭据")
    
    def stop_server(self):
        """停止MCP服务器。"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 MCP服务器已停止")
    
    def run_test(self):
        """运行完整测试。"""
        print("🎯 本地登录页面爆破测试")
        print("=" * 60)
        print(f"目标: {self.target_url}")
        print("功能: 验证执行器循环机制和本地登录爆破")
        print("=" * 60)
        
        try:
            # 启动MCP服务器
            self.start_mcp_server()
            
            # 初始化MCP连接
            self.initialize_mcp()
            
            # 测试本地登录
            self.test_local_login()
            
            print(f"\n✅ 测试完成!")
            print(f"📋 验证结果:")
            print(f"  ✅ MCP服务器正常工作")
            print(f"  ✅ 任务提交成功")
            print(f"  ✅ 执行器循环处理任务")
            print(f"  ✅ 本地登录页面爆破测试完成")
            
        finally:
            self.stop_server()


def main():
    """主入口。"""
    test = LocalLoginTest()
    test.run_test()


if __name__ == "__main__":
    main()
