#!/usr/bin/env python3
"""
Test script for Web Brute Force API.
"""

import requests
import time
import json
from pathlib import Path


def test_api_connection():
    """Test basic API connection."""
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ API server is running")
            print(f"📋 Response: {response.json()}")
            return True
        else:
            print(f"❌ API server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Is it running?")
        return False
    except Exception as e:
        print(f"❌ Error testing API connection: {e}")
        return False


def create_test_files():
    """Create test dictionary files."""
    print("📝 Creating test files...")
    
    # Ensure directories exist
    Path("dicts").mkdir(exist_ok=True)
    Path("templates").mkdir(exist_ok=True)
    
    # Create test username dictionary
    with open("dicts/username.txt", "w") as f:
        f.write("admin\ntest\nuser\n")
    
    # Create test password dictionary  
    with open("dicts/password.txt", "w") as f:
        f.write("admin\npassword\n123456\n")
    
    # Create a simple test template
    template_content = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "http://httpbin.org/status/200"
      waitUntil: load
  
  check:
    action: check
    success:
      - "body"
    failure:
      - ".error"
"""
    
    with open("templates/test-simple.yaml", "w") as f:
        f.write(template_content)
    
    print("✅ Test files created")


def test_task_submission():
    """Test task submission."""
    print("\n🚀 Testing task submission...")
    
    task_data = {
        "url": "http://httpbin.org/status/200",
        "username_dict_path": "dicts/username.txt",
        "password_dict_path": "dicts/password.txt",
        "config_template": "templates/test-simple.yaml"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/tasks", json=task_data)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            print(f"✅ Task submitted successfully: {task_id}")
            return task_id
        else:
            print(f"❌ Task submission failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error submitting task: {e}")
        return None


def test_task_query(task_id):
    """Test task query."""
    print(f"\n🔍 Testing task query for: {task_id}")
    
    try:
        response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
        
        if response.status_code == 200:
            result = response.json()
            task = result["task"]
            print(f"✅ Task query successful")
            print(f"📊 Status: {task['status']}")
            print(f"📈 Progress: {task['current_attempts']}/{task['total_attempts']}")
            print(f"🎯 Found credentials: {task['found_credentials']}")
            return result
        else:
            print(f"❌ Task query failed: {response.status_code}")
            print(f"📋 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error querying task: {e}")
        return None


def test_task_list():
    """Test task listing."""
    print("\n📋 Testing task list...")
    
    try:
        response = requests.get("http://localhost:8000/api/tasks")
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ Task list retrieved: {len(tasks)} tasks")
            for task in tasks[:3]:  # Show first 3 tasks
                print(f"  📌 {task['id'][:8]}... - {task['status']} - {task['url']}")
            return tasks
        else:
            print(f"❌ Task list failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error listing tasks: {e}")
        return None


def main():
    """Main test function."""
    print("🧪 Web Brute Force API Test Suite")
    print("=" * 50)
    
    # Test API connection
    if not test_api_connection():
        print("\n❌ API server is not accessible. Please start it first:")
        print("   python -m uvicorn web_server:app --host 0.0.0.0 --port 8000")
        return
    
    # Create test files
    create_test_files()
    
    # Test task submission
    task_id = test_task_submission()
    if not task_id:
        print("\n❌ Cannot proceed without successful task submission")
        return
    
    # Wait a moment
    print("\n⏳ Waiting 2 seconds...")
    time.sleep(2)
    
    # Test task query
    task_data = test_task_query(task_id)
    if not task_data:
        print("\n❌ Task query failed")
        return
    
    # Test task list
    test_task_list()
    
    print("\n" + "=" * 50)
    print("🎉 All API tests completed!")
    print("\n📚 Next steps:")
    print("1. Start the executor service: python brute_force_executor.py")
    print("2. Monitor task progress with: GET /api/tasks/{task_id}")
    print("3. View API documentation at: http://localhost:8000/docs")


if __name__ == "__main__":
    main()
