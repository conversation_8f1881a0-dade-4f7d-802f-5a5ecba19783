#!/usr/bin/env python3
"""
Simple startup script for Web Brute Force API system.
"""

import os
import sys

def main():
    """Main entry point."""
    print("🎯 Web Brute Force API System")
    print("=" * 40)
    print()
    print("Choose startup mode:")
    print("1. Start both API server and executor service (recommended)")
    print("2. Start only API server")
    print("3. Start only executor service")
    print("4. Initialize database only")
    print()
    
    try:
        choice = input("Enter your choice (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 Starting both services...")
            os.system("python start_services.py")
        
        elif choice == "2":
            print("\n🌐 Starting API server only...")
            print("📡 API will be available at: http://localhost:8000")
            print("📚 API documentation at: http://localhost:8000/docs")
            os.system("python -m uvicorn web_server:app --host 0.0.0.0 --port 8000 --reload")
        
        elif choice == "3":
            print("\n⚙️  Starting executor service only...")
            os.system("python brute_force_executor.py")
        
        elif choice == "4":
            print("\n🗄️  Initializing database...")
            from models import init_database
            init_database()
            print("✅ Database initialized successfully!")
        
        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n👋 Cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
