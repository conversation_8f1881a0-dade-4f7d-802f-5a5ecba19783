#!/usr/bin/env python3
"""
Demo script for Web Brute Force API System.
Demonstrates the complete workflow.
"""

import requests
import time
import json
from datetime import datetime


def print_banner():
    """Print demo banner."""
    print("🎯" + "=" * 58 + "🎯")
    print("🎯" + " " * 20 + "WEB BRUTE FORCE API DEMO" + " " * 14 + "🎯")
    print("🎯" + "=" * 58 + "🎯")
    print()


def check_services():
    """Check if services are running."""
    print("🔍 Checking services...")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ API Server is running")
            return True
        else:
            print(f"❌ API Server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API Server is not running")
        print("💡 Please start services with: python start_services.py")
        return False
    except Exception as e:
        print(f"❌ Error checking services: {e}")
        return False


def show_api_info():
    """Show API information."""
    print("\n📚 API Information:")
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            info = response.json()
            print(f"  📋 Version: {info['version']}")
            print(f"  🔗 Endpoints:")
            for name, endpoint in info['endpoints'].items():
                print(f"    • {name}: {endpoint}")
    except Exception as e:
        print(f"❌ Error getting API info: {e}")


def submit_demo_task():
    """Submit a demo task."""
    print("\n🚀 Submitting demo task...")

    # Sample LLM-generated template
    sample_template = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load

  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='email'], #username"
    parameters:
      text: "{username}"

  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password"
    parameters:
      text: "{password}"

  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn"

  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
    failure:
      - ".error"
      - ".alert-danger"
"""

    # New API - URL + LLM-generated template
    task_data = {
        "url": "http://httpbin.org/status/200",
        "template": sample_template
    }

    print(f"  🌐 Target URL: {task_data['url']}")
    print(f"  🤖 Template: LLM-generated YAML configuration")
    print(f"  📝 Template preview: {len(sample_template)} characters")
    
    try:
        response = requests.post("http://localhost:8000/api/tasks", json=task_data)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            print(f"✅ Task submitted successfully!")
            print(f"  📋 Task ID: {task_id}")
            print(f"  📊 Configuration used:")
            config = result["configuration"]
            print(f"    👤 Username dict: {config['username_dict']}")
            print(f"    🔑 Password dict: {config['password_dict']}")
            print(f"    📋 Template file: {config['config_template']}")
            print(f"  💬 {result['message']}")
            return task_id
        else:
            print(f"❌ Task submission failed: {response.status_code}")
            print(f"  📋 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error submitting task: {e}")
        return None


def monitor_task_with_animation(task_id, max_wait_time=60):
    """Monitor task with animated progress."""
    print(f"\n📊 Monitoring task progress...")
    print(f"  📋 Task ID: {task_id}")
    
    start_time = time.time()
    last_status = None
    animation_chars = "⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏"
    animation_index = 0
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                task = result["task"]
                
                status = task["status"]
                progress = f"{task['current_attempts']}/{task['total_attempts']}"
                
                # Show animated progress for running tasks
                if status == "running":
                    char = animation_chars[animation_index % len(animation_chars)]
                    print(f"\r  {char} Status: {status}, Progress: {progress}", end="", flush=True)
                    animation_index += 1
                elif status != last_status:
                    print(f"\r  📈 Status: {status}, Progress: {progress}")
                    last_status = status
                
                # Check if task is complete
                if status in ["completed", "failed", "cancelled"]:
                    print(f"\n🏁 Task finished with status: {status}")
                    
                    # Show results
                    print(f"\n📊 Final Results:")
                    print(f"  ⏱️  Duration: {(datetime.fromisoformat(task['completed_at'].replace('Z', '+00:00')) - datetime.fromisoformat(task['created_at'].replace('Z', '+00:00'))).total_seconds():.1f}s")
                    print(f"  🔢 Total Attempts: {task['total_attempts']}")
                    print(f"  ✅ Successful Attempts: {task['successful_attempts']}")
                    print(f"  🎯 Found Credentials: {task['found_credentials']}")
                    
                    if task["found_credentials"]:
                        print(f"\n🎉 Found credentials:")
                        for result_item in result["results"]:
                            print(f"  👤 {result_item['username']}:{result_item['password']}")
                    
                    if result["recent_logs"]:
                        print(f"\n📝 Recent execution logs:")
                        for log in result["recent_logs"][-3:]:  # Last 3 logs
                            level_emoji = {"INFO": "ℹ️", "SUCCESS": "✅", "ERROR": "❌", "WARNING": "⚠️"}.get(log['level'], "📝")
                            print(f"  {level_emoji} {log['message']}")
                    
                    return result
                
                time.sleep(1)  # Wait 1 second before next check
                
            else:
                print(f"\n❌ Error querying task: {response.status_code}")
                break
                
        except Exception as e:
            print(f"\n❌ Error monitoring task: {e}")
            break
    
    print(f"\n⏰ Monitoring timeout after {max_wait_time} seconds")
    return None


def show_all_tasks():
    """Show all tasks in the system."""
    print("\n📋 All tasks in system:")
    
    try:
        response = requests.get("http://localhost:8000/api/tasks")
        if response.status_code == 200:
            tasks = response.json()
            
            if not tasks:
                print("  📭 No tasks found")
                return
            
            for i, task in enumerate(tasks[:5], 1):  # Show first 5 tasks
                status_emoji = {
                    "pending": "⏳",
                    "running": "🔄", 
                    "completed": "✅",
                    "failed": "❌",
                    "cancelled": "🚫"
                }.get(task['status'], "❓")
                
                print(f"  {i}. {status_emoji} {task['id'][:8]}... - {task['status']} - {task['url']}")
                print(f"     📅 Created: {task['created_at']}")
                
                if task['found_credentials']:
                    print(f"     🎯 Found credentials!")
                
                if task['error_message']:
                    print(f"     ❌ Error: {task['error_message'][:50]}...")
                
                print()
                
        else:
            print(f"❌ Error getting tasks: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error listing tasks: {e}")


def main():
    """Main demo function."""
    print_banner()
    
    # Check services
    if not check_services():
        return
    
    # Show API info
    show_api_info()
    
    # Show existing tasks
    show_all_tasks()
    
    # Ask user if they want to submit a new task
    print("🤔 Would you like to submit a new demo task? (y/n): ", end="")
    try:
        choice = input().strip().lower()
        if choice not in ['y', 'yes']:
            print("👋 Demo completed!")
            return
    except KeyboardInterrupt:
        print("\n👋 Demo cancelled!")
        return
    
    # Submit demo task
    task_id = submit_demo_task()
    if not task_id:
        print("❌ Cannot proceed without successful task submission")
        return
    
    print(f"\n⏳ Waiting for executor service to pick up the task...")
    time.sleep(2)
    
    # Monitor task progress
    result = monitor_task_with_animation(task_id, max_wait_time=120)
    
    if result:
        print(f"\n🎉 Demo completed successfully!")
    else:
        print(f"\n❌ Demo failed or timed out")
    
    print(f"\n🔗 Useful URLs:")
    print(f"  📡 API Server: http://localhost:8000")
    print(f"  📚 API Documentation: http://localhost:8000/docs")
    print(f"  🔄 Interactive API: http://localhost:8000/redoc")
    
    print(f"\n💡 Tips:")
    print(f"  • Use curl or Postman to interact with the API")
    print(f"  • Check the database file: brute_force.db")
    print(f"  • Modify dictionary files in dicts/ directory")
    print(f"  • Create custom templates in templates/ directory")


if __name__ == "__main__":
    main()
