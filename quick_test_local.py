#!/usr/bin/env python3
"""
快速测试本地登录页面，验证增强的调试信息。
"""

import json
import subprocess
import sys
import time
from pathlib import Path


def quick_test():
    """快速测试本地登录页面。"""
    print("🎯 快速测试本地登录页面")
    print("=" * 50)
    
    # 启动MCP服务器
    server_script = Path(__file__).parent / "brute_force_mcp_server.py"
    print(f"🚀 启动MCP服务器...")
    
    server_process = subprocess.Popen(
        [sys.executable, str(server_script)],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1
    )
    
    try:
        time.sleep(3)
        print("✅ MCP服务器已启动")
        
        # 初始化MCP连接
        print("🔧 初始化MCP连接...")
        
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "quick-test", "version": "1.0.0"}
            }
        }
        
        server_process.stdin.write(json.dumps(init_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ MCP连接成功")
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        server_process.stdin.flush()
        
        # 提交任务
        print(f"📤 提交爆破任务到: http://127.0.0.1:9001/login")
        
        template = """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[name='user'], input[type='text'], #username, #user"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password, #pass"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login, .btn-login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - ".home"
      - ".success"
      - "text=Welcome"
      - "text=Dashboard"
      - "text=Success"
    failure:
      - ".error"
      - ".alert-danger"
      - ".login-error"
      - "text=Invalid"
      - "text=Login failed"
      - "text=Error"
"""
        
        submit_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": "submit_brute_force_task",
                "arguments": {
                    "url": "http://127.0.0.1:9001/login",
                    "template": template
                }
            }
        }
        
        server_process.stdin.write(json.dumps(submit_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                result = json.loads(response["result"]["content"][0]["text"])
                if result["status"] == "success":
                    task_id = result["task_id"]
                    print(f"✅ 任务提交成功: {task_id}")
                    print(f"📝 模板文件: {result['configuration']['config_template']}")
                    print(f"🔄 执行器将开始处理任务，请查看执行器终端的详细调试信息...")
                    
                    # 等待一段时间让用户观察执行器输出
                    print(f"\n⏳ 等待30秒让任务执行...")
                    time.sleep(30)
                    
                else:
                    print(f"❌ 任务提交失败: {result.get('error', 'Unknown error')}")
            else:
                print(f"❌ 响应格式错误: {response}")
        else:
            print(f"❌ 无响应")
        
    finally:
        print("🛑 停止MCP服务器...")
        server_process.terminate()
        server_process.wait()
        print("✅ 测试完成")


if __name__ == "__main__":
    quick_test()
