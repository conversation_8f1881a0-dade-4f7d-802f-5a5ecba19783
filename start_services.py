#!/usr/bin/env python3
"""
Service management script for Web Brute Force system.
Manages both API server and executor service.
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path


class ServiceManager:
    """Manages multiple services."""
    
    def __init__(self):
        self.processes = {}
        self.running = True
    
    def check_dependencies(self):
        """Check if all required files and directories exist."""
        required_dirs = ["dicts", "templates"]
        required_files = [
            "dicts/username.txt",
            "dicts/password.txt", 
            "templates/test-grafana.yaml"
        ]
        
        print("🔍 Checking dependencies...")
        
        # Check directories
        for directory in required_dirs:
            if not os.path.exists(directory):
                print(f"❌ Missing directory: {directory}")
                return False
            else:
                print(f"✅ Directory found: {directory}")
        
        # Check files
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"❌ Missing file: {file_path}")
                return False
            else:
                print(f"✅ File found: {file_path}")
        
        return True
    
    def create_sample_files(self):
        """Create sample dictionary files if they don't exist."""
        print("📝 Creating sample files...")
        
        # Ensure directories exist
        os.makedirs("dicts", exist_ok=True)
        os.makedirs("templates", exist_ok=True)
        
        # Create sample username dictionary
        username_file = "dicts/username.txt"
        if not os.path.exists(username_file):
            with open(username_file, 'w', encoding='utf-8') as f:
                f.write("admin\n")
                f.write("administrator\n")
                f.write("root\n")
                f.write("user\n")
                f.write("test\n")
                f.write("guest\n")
            print(f"✅ Created sample file: {username_file}")
        
        # Create sample password dictionary
        password_file = "dicts/password.txt"
        if not os.path.exists(password_file):
            with open(password_file, 'w', encoding='utf-8') as f:
                f.write("admin\n")
                f.write("password\n")
                f.write("123456\n")
                f.write("admin123\n")
                f.write("root\n")
                f.write("test\n")
                f.write("guest\n")
            print(f"✅ Created sample file: {password_file}")
    
    def init_database(self):
        """Initialize the database."""
        print("🗄️  Initializing database...")
        try:
            from models import init_database
            init_database()
            print("✅ Database initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            return False
    
    def start_service(self, name, command, cwd=None):
        """Start a service process."""
        print(f"🚀 Starting {name}...")
        try:
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=cwd or os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            self.processes[name] = process
            
            # Start output monitoring thread
            thread = threading.Thread(
                target=self._monitor_output,
                args=(name, process),
                daemon=True
            )
            thread.start()
            
            print(f"✅ {name} started with PID: {process.pid}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start {name}: {e}")
            return False
    
    def _monitor_output(self, name, process):
        """Monitor process output."""
        while self.running and process.poll() is None:
            try:
                line = process.stdout.readline()
                if line:
                    print(f"[{name}] {line.strip()}")
            except:
                break
    
    def stop_services(self):
        """Stop all services gracefully."""
        print("\n🛑 Stopping all services...")
        self.running = False
        
        for name, process in self.processes.items():
            if process.poll() is None:
                print(f"⏹️  Stopping {name}...")
                try:
                    process.terminate()
                    process.wait(timeout=10)
                    print(f"✅ {name} stopped")
                except subprocess.TimeoutExpired:
                    print(f"⚠️  Force killing {name}...")
                    process.kill()
                    process.wait()
                    print(f"💀 {name} force killed")
                except Exception as e:
                    print(f"❌ Error stopping {name}: {e}")
    
    def wait_for_services(self):
        """Wait for all services to complete."""
        try:
            while self.running and any(p.poll() is None for p in self.processes.values()):
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 Received interrupt signal")
    
    def run(self):
        """Main service management loop."""
        print("🎯 Web Brute Force Service Manager")
        print("=" * 50)
        
        # Initialize database
        if not self.init_database():
            sys.exit(1)
        
        # Check dependencies
        if not self.check_dependencies():
            print("\n⚠️  Some dependencies are missing.")
            print("Creating sample files...")
            self.create_sample_files()
            
            # Check again
            if not self.check_dependencies():
                print("❌ Failed to create required files. Please check permissions.")
                sys.exit(1)
        
        print("\n✅ All dependencies satisfied!")
        print("=" * 50)
        
        # Start services
        services_started = 0
        
        # Start API server
        if self.start_service(
            "API Server",
            "python -m uvicorn web_server:app --host 0.0.0.0 --port 8000 --reload"
        ):
            services_started += 1
        
        # Wait a bit for API server to start
        time.sleep(2)
        
        # Start executor service
        if self.start_service(
            "Executor Service",
            "python brute_force_executor.py"
        ):
            services_started += 1
        
        if services_started == 0:
            print("❌ No services started successfully")
            sys.exit(1)
        
        print("\n🌐 Services Information:")
        print("📡 API Server: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("🔄 Interactive API: http://localhost:8000/redoc")
        print("=" * 50)
        print("Press Ctrl+C to stop all services")
        
        # Wait for services
        self.wait_for_services()
        
        # Cleanup
        self.stop_services()
        print("🔚 All services stopped")


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    print(f"\n📡 Received signal {signum}")
    if 'manager' in globals():
        manager.stop_services()


def main():
    """Main entry point."""
    global manager
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    manager = ServiceManager()
    try:
        manager.run()
    except Exception as e:
        print(f"❌ Service manager error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
