#!/usr/bin/env python3
"""
FastMCP Server for Web Brute Force API.
Provides MCP tools to submit URLs with templates and track brute force progress.
"""

import asyncio
import json
import os
import uuid
import yaml
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from fastmcp import FastMCP
from sqlalchemy.orm import Session
from sqlalchemy import desc

from models import (
    SessionLocal, init_database, BruteForceTask, BruteForceResult, TaskLog, TaskStatus
)

# Initialize FastMCP server
mcp = FastMCP("brute-force-api")

# Initialize database on startup
init_database()


def ensure_templates_directory():
    """Ensure templates directory exists."""
    templates_dir = "templates"
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    return templates_dir


def save_template_to_file(template_content: str) -> str:
    """Save template content to a file and return the file path."""
    templates_dir = ensure_templates_directory()
    
    # Generate unique filename
    template_id = str(uuid.uuid4())
    filename = f"llm-generated-{template_id}.yaml"
    filepath = os.path.join(templates_dir, filename)
    
    try:
        # Validate YAML format
        yaml.safe_load(template_content)
        
        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        return filepath
        
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML template: {str(e)}")
    except Exception as e:
        raise RuntimeError(f"Failed to save template: {str(e)}")


@mcp.tool()
async def submit_brute_force_task(url: str, template: str) -> str:
    """
    Submit a new brute force task with URL and LLM-generated template.
    
    Args:
        url: Target URL for brute force testing
        template: YAML template content generated by LLM
    
    Returns:
        JSON string with task submission result
    """
    try:
        # Validate URL format
        if not url.startswith(('http://', 'https://')):
            raise ValueError("URL must start with http:// or https://")
        
        # Use default dictionary paths
        default_username_dict = "dicts/username.txt"
        default_password_dict = "dicts/password.txt"
        
        # Validate default dictionary files exist
        if not os.path.exists(default_username_dict):
            raise FileNotFoundError(f"Default username dictionary not found: {default_username_dict}")
        
        if not os.path.exists(default_password_dict):
            raise FileNotFoundError(f"Default password dictionary not found: {default_password_dict}")
        
        # Save the LLM-generated template to file
        template_filepath = save_template_to_file(template)
        
        # Create database session
        db = SessionLocal()
        try:
            # Create new task with LLM-generated template
            task = BruteForceTask(
                url=url,
                username_dict_path=default_username_dict,
                password_dict_path=default_password_dict,
                config_template=template_filepath,
                status=TaskStatus.PENDING.value
            )
            
            db.add(task)
            db.commit()
            db.refresh(task)
            
            result = {
                "status": "success",
                "task_id": task.id,
                "url": task.url,
                "configuration": {
                    "username_dict": default_username_dict,
                    "password_dict": default_password_dict,
                    "config_template": template_filepath
                },
                "task_status": "pending",
                "message": "Task submitted successfully. Template saved and ready for execution."
            }
            
            return json.dumps(result, indent=2)
            
        finally:
            db.close()
            
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def get_task_status(task_id: str) -> str:
    """
    Get detailed information about a specific brute force task.
    
    Args:
        task_id: Task ID to retrieve
    
    Returns:
        JSON string with detailed task information, results, and logs
    """
    try:
        db = SessionLocal()
        try:
            task = db.query(BruteForceTask).filter(BruteForceTask.id == task_id).first()
            if not task:
                raise ValueError(f"Task not found: {task_id}")
            
            # Get results
            results = [
                {
                    "username": result.username,
                    "password": result.password,
                    "discovered_at": result.discovered_at.isoformat(),
                    "response_details": result.response_details
                }
                for result in task.results
            ]
            
            # Get recent logs (last 20)
            recent_logs = [
                {
                    "timestamp": log.timestamp.isoformat(),
                    "level": log.level,
                    "message": log.message,
                    "username_attempted": log.username_attempted,
                    "password_attempted": log.password_attempted
                }
                for log in db.query(TaskLog)
                .filter(TaskLog.task_id == task_id)
                .order_by(desc(TaskLog.timestamp))
                .limit(20)
                .all()
            ]
            
            task_info = {
                "status": "success",
                "task": {
                    "id": task.id,
                    "url": task.url,
                    "status": task.status,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "total_attempts": task.total_attempts,
                    "current_attempts": task.current_attempts,
                    "successful_attempts": task.successful_attempts,
                    "found_credentials": task.found_credentials,
                    "error_message": task.error_message,
                    "config_template": task.config_template
                },
                "results": results,
                "recent_logs": recent_logs
            }
            
            return json.dumps(task_info, indent=2)
            
        finally:
            db.close()
            
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def list_tasks(
    status: Optional[str] = None,
    limit: int = 10,
    offset: int = 0
) -> str:
    """
    List brute force tasks with optional filtering.
    
    Args:
        status: Optional status filter (pending, running, completed, failed, cancelled)
        limit: Maximum number of tasks to return (default: 10)
        offset: Number of tasks to skip (default: 0)
    
    Returns:
        JSON string with list of tasks
    """
    try:
        db = SessionLocal()
        try:
            query = db.query(BruteForceTask)
            
            if status:
                if status not in [s.value for s in TaskStatus]:
                    raise ValueError(f"Invalid status: {status}. Valid statuses: {[s.value for s in TaskStatus]}")
                query = query.filter(BruteForceTask.status == status)
            
            tasks = query.order_by(desc(BruteForceTask.created_at)).offset(offset).limit(limit).all()
            
            task_list = [
                {
                    "id": task.id,
                    "url": task.url,
                    "status": task.status,
                    "created_at": task.created_at.isoformat(),
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "total_attempts": task.total_attempts,
                    "current_attempts": task.current_attempts,
                    "successful_attempts": task.successful_attempts,
                    "found_credentials": task.found_credentials,
                    "error_message": task.error_message
                }
                for task in tasks
            ]
            
            result = {
                "status": "success",
                "tasks": task_list,
                "total_returned": len(task_list),
                "filter": {
                    "status": status,
                    "limit": limit,
                    "offset": offset
                }
            }
            
            return json.dumps(result, indent=2)
            
        finally:
            db.close()
            
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def cancel_task(task_id: str) -> str:
    """
    Cancel a running brute force task.
    
    Args:
        task_id: Task ID to cancel
    
    Returns:
        JSON string with cancellation result
    """
    try:
        db = SessionLocal()
        try:
            task = db.query(BruteForceTask).filter(BruteForceTask.id == task_id).first()
            if not task:
                raise ValueError(f"Task not found: {task_id}")
            
            if task.status in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]:
                raise ValueError(f"Cannot cancel task with status: {task.status}")
            
            task.status = TaskStatus.CANCELLED.value
            task.completed_at = datetime.now(timezone.utc)
            db.commit()
            
            result = {
                "status": "success",
                "task_id": task_id,
                "message": f"Task {task_id} cancelled successfully",
                "previous_status": task.status,
                "cancelled_at": task.completed_at.isoformat()
            }
            
            return json.dumps(result, indent=2)
            
        finally:
            db.close()
            
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def get_task_results(task_id: str) -> str:
    """
    Get only the successful results from a completed brute force task.
    
    Args:
        task_id: Task ID to get results for
    
    Returns:
        JSON string with successful credentials found
    """
    try:
        db = SessionLocal()
        try:
            task = db.query(BruteForceTask).filter(BruteForceTask.id == task_id).first()
            if not task:
                raise ValueError(f"Task not found: {task_id}")
            
            results = [
                {
                    "username": result.username,
                    "password": result.password,
                    "discovered_at": result.discovered_at.isoformat(),
                    "response_details": result.response_details
                }
                for result in task.results
            ]
            
            result = {
                "status": "success",
                "task_id": task_id,
                "task_status": task.status,
                "found_credentials": task.found_credentials,
                "successful_attempts": task.successful_attempts,
                "results": results,
                "summary": {
                    "total_credentials_found": len(results),
                    "task_completed": task.status == TaskStatus.COMPLETED.value
                }
            }
            
            return json.dumps(result, indent=2)
            
        finally:
            db.close()
            
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__
        }
        return json.dumps(error_result, indent=2)


if __name__ == "__main__":
    print("🚀 Starting Brute Force MCP Server...")
    print("Available tools:")
    print("- submit_brute_force_task: Submit URL with LLM-generated template")
    print("- get_task_status: Get detailed task information and progress")
    print("- list_tasks: List all tasks with optional filtering")
    print("- cancel_task: Cancel a running task")
    print("- get_task_results: Get successful credentials from completed task")
    print()
    print("Server is ready to accept MCP connections...")

    # Run the MCP server
    try:
        mcp.run()
    except RuntimeError as e:
        if "already running" in str(e).lower():
            print("⚠️  AsyncIO loop already running, using alternative startup...")
            import nest_asyncio
            nest_asyncio.apply()
            mcp.run()
        else:
            raise
