#!/usr/bin/env python3
"""
FastMCP Server for Playwright DOM extraction.
Provides tools to load web pages and extract DOM content using Playwright.
"""

import asyncio
import json
import re
from typing import Any, Dict, Optional, List, Tuple
from playwright.async_api import async_playwright, <PERSON>rowser, <PERSON>
from fastmcp import FastMCP

# Initialize FastMCP server
mcp = FastMCP("playwright-dom-extractor")

# Global browser state
playwright_instance = None
browser: Optional[Browser] = None
page: Optional[Page] = None


async def ensure_browser(headless: bool = True):
    """Ensure browser is initialized."""
    global playwright_instance, browser, page
    
    if not playwright_instance:
        playwright_instance = await async_playwright().start()
    
    if not browser:
        browser = await playwright_instance.chromium.launch(headless=headless)
    
    if not page:
        page = await browser.new_page()


@mcp.tool()
async def load_page(
    url: str,
    wait_for: str = "load",
    timeout: int = 30000,
    headless: bool = True
) -> str:
    """
    Load a web page using <PERSON><PERSON> and extract its DOM.
    
    Args:
        url: The URL to load
        wait_for: What to wait for after loading (load, domcontentloaded, networkidle)
        timeout: Timeout in milliseconds
        headless: Run browser in headless mode
    
    Returns:
        JSON string with page load result
    """
    global page
    
    try:
        await ensure_browser(headless)

        # Navigate to the page
        await page.goto(url, wait_until=wait_for, timeout=timeout)

        # Wait a bit more for JavaScript to load dynamic content
        if wait_for != "networkidle":
            try:
                await page.wait_for_load_state("networkidle", timeout=5000)
            except:
                pass  # Continue even if networkidle timeout

        # Wait for common login form elements to appear
        try:
            await page.wait_for_selector("input, form, button", timeout=3000)
        except:
            pass  # Continue even if no form elements found

        # Get basic page info
        title = await page.title()
        current_url = page.url

        result = {
            "status": "success",
            "url": current_url,
            "title": title,
            "message": f"Successfully loaded page: {title}"
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "url": url,
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def get_dom(
    selector: Optional[str] = None,
    include_styles: bool = False,
    pretty_print: bool = True
) -> str:
    """
    Get the current page's DOM content.
    
    Args:
        selector: CSS selector to extract specific elements (optional)
        include_styles: Include computed styles
        pretty_print: Format the HTML output
    
    Returns:
        JSON string with DOM content
    """
    global page
    
    if not page:
        return json.dumps({"error": "No page loaded. Use load_page first."}, indent=2)
    
    try:
        if selector:
            # Get specific elements
            elements = await page.locator(selector).all()
            if not elements:
                return json.dumps({"error": f"No elements found for selector: {selector}"}, indent=2)
            
            html_content = ""
            for element in elements:
                html_content += await element.inner_html()
                html_content += "\n"
        else:
            # Get full page HTML
            html_content = await page.content()
        
        result = {
            "status": "success",
            "url": page.url,
            "selector": selector or "full page",
            "html": html_content
        }
        
        if include_styles:
            # Get computed styles for elements
            styles_script = """
            () => {
                const styles = {};
                const elements = document.querySelectorAll('*');
                elements.forEach((el, index) => {
                    if (index < 100) { // Limit to first 100 elements
                        const computedStyle = window.getComputedStyle(el);
                        styles[el.tagName + '_' + index] = {
                            display: computedStyle.display,
                            position: computedStyle.position,
                            width: computedStyle.width,
                            height: computedStyle.height
                        };
                    }
                });
                return styles;
            }
            """
            styles = await page.evaluate(styles_script)
            result["styles"] = styles
        
        return json.dumps(result, indent=2 if pretty_print else None)
        
    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def find_login_elements(include_html: bool = True) -> str:
    """
    Find common login form elements on the current page.
    
    Args:
        include_html: Include HTML content of found elements
    
    Returns:
        JSON string with found login elements
    """
    global page
    
    if not page:
        return json.dumps({"error": "No page loaded. Use load_page first."}, indent=2)

    try:
        # Common selectors for login elements
        login_selectors = {
            "forms": "form",
            "username_inputs": "input[type='text'], input[type='email'], input[name*='user'], input[name*='login'], input[name*='email'], input[id*='user'], input[id*='login'], input[id*='email']",
            "password_inputs": "input[type='password'], input[name*='pass'], input[id*='pass']",
            "submit_buttons": "button[type='submit'], input[type='submit'], button:has-text('login'), button:has-text('sign in'), button:has-text('submit')",
            "all_inputs": "input",
            "all_buttons": "button"
        }

        found_elements = {}

        for element_type, selector in login_selectors.items():
            try:
                elements = await page.locator(selector).all()
                element_info = []

                for i, element in enumerate(elements):
                    info = {
                        "index": i,
                        "tag": await element.evaluate("el => el.tagName.toLowerCase()"),
                        "type": await element.get_attribute("type") or "",
                        "name": await element.get_attribute("name") or "",
                        "id": await element.get_attribute("id") or "",
                        "class": await element.get_attribute("class") or "",
                        "placeholder": await element.get_attribute("placeholder") or "",
                        "value": await element.get_attribute("value") or "",
                        "visible": await element.is_visible()
                    }

                    if include_html:
                        try:
                            info["html"] = await element.evaluate("el => el.outerHTML")
                        except:
                            info["html"] = "Could not extract HTML"

                    element_info.append(info)

                found_elements[element_type] = {
                    "count": len(elements),
                    "elements": element_info
                }

            except Exception as e:
                found_elements[element_type] = {
                    "count": 0,
                    "error": str(e),
                    "elements": []
                }

        result = {
            "status": "success",
            "url": page.url,
            "found_elements": found_elements,
            "summary": {
                "total_forms": found_elements.get("forms", {}).get("count", 0),
                "total_username_inputs": found_elements.get("username_inputs", {}).get("count", 0),
                "total_password_inputs": found_elements.get("password_inputs", {}).get("count", 0),
                "total_submit_buttons": found_elements.get("submit_buttons", {}).get("count", 0),
                "total_inputs": found_elements.get("all_inputs", {}).get("count", 0),
                "total_buttons": found_elements.get("all_buttons", {}).get("count", 0)
            }
        }

        return json.dumps(result, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


@mcp.tool()
async def snapshot_for_ai(
    include_screenshots: bool = False,
    max_elements: int = 100,
    include_invisible: bool = False
) -> str:
    """
    Create an AI-friendly snapshot of the current page with structured information.

    Args:
        include_screenshots: Whether to include element screenshots
        max_elements: Maximum number of interactive elements to analyze
        include_invisible: Whether to include invisible elements

    Returns:
        JSON string with structured page information for AI analysis
    """
    global page

    if not page:
        return json.dumps({"error": "No page loaded. Use load_page first."}, indent=2)

    try:
        # Get basic page information
        title = await page.title()
        url = page.url
        viewport = await page.viewport_size()

        # Extract structured content
        page_info = {
            "status": "success",
            "url": url,
            "title": title,
            "viewport": viewport,
            "timestamp": asyncio.get_event_loop().time(),
            "content": {
                "text_content": await _extract_text_content(),
                "interactive_elements": await _extract_interactive_elements(max_elements, include_invisible),
                "forms": await _extract_forms_info(),
                "navigation": await _extract_navigation_info(),
                "layout": await _extract_layout_info(),
                "accessibility": await _extract_accessibility_info()
            }
        }

        if include_screenshots:
            page_info["screenshots"] = await _capture_element_screenshots()

        return json.dumps(page_info, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


async def _extract_text_content() -> Dict:
    """Extract structured text content from the page."""
    global page

    text_script = """
    () => {
        const result = {
            headings: [],
            paragraphs: [],
            lists: [],
            links: [],
            important_text: []
        };

        // Extract headings
        document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach((el, index) => {
            if (el.textContent.trim()) {
                result.headings.push({
                    level: parseInt(el.tagName.charAt(1)),
                    text: el.textContent.trim(),
                    id: el.id || null,
                    index: index
                });
            }
        });

        // Extract paragraphs
        document.querySelectorAll('p').forEach((el, index) => {
            const text = el.textContent.trim();
            if (text && text.length > 10) {
                result.paragraphs.push({
                    text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
                    index: index
                });
            }
        });

        // Extract lists
        document.querySelectorAll('ul, ol').forEach((el, index) => {
            const items = Array.from(el.querySelectorAll('li')).map(li => li.textContent.trim()).filter(text => text);
            if (items.length > 0) {
                result.lists.push({
                    type: el.tagName.toLowerCase(),
                    items: items.slice(0, 10), // Limit to first 10 items
                    index: index
                });
            }
        });

        // Extract links
        document.querySelectorAll('a[href]').forEach((el, index) => {
            const text = el.textContent.trim();
            const href = el.href;
            if (text && href) {
                result.links.push({
                    text: text,
                    href: href,
                    index: index
                });
            }
        });

        // Extract important text (strong, em, mark, etc.)
        document.querySelectorAll('strong, b, em, i, mark, .important, .highlight').forEach((el, index) => {
            const text = el.textContent.trim();
            if (text) {
                result.important_text.push({
                    text: text,
                    tag: el.tagName.toLowerCase(),
                    class: el.className || null,
                    index: index
                });
            }
        });

        return result;
    }
    """

    return await page.evaluate(text_script)


async def _extract_interactive_elements(max_elements: int, include_invisible: bool) -> List[Dict]:
    """Extract interactive elements with their properties."""
    global page

    interactive_script = f"""
    (maxElements, includeInvisible) => {{
        const elements = [];
        const selectors = [
            'button', 'input', 'select', 'textarea', 'a[href]',
            '[onclick]', '[role="button"]', '[role="link"]',
            '[tabindex]', '.clickable', '.btn'
        ];

        const allElements = document.querySelectorAll(selectors.join(', '));

        for (let i = 0; i < Math.min(allElements.length, maxElements); i++) {{
            const el = allElements[i];
            const rect = el.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0 &&
                             window.getComputedStyle(el).visibility !== 'hidden' &&
                             window.getComputedStyle(el).display !== 'none';

            if (!includeInvisible && !isVisible) continue;

            const elementInfo = {{
                tag: el.tagName.toLowerCase(),
                type: el.type || null,
                id: el.id || null,
                class: el.className || null,
                name: el.name || null,
                value: el.value || null,
                text: el.textContent ? el.textContent.trim().substring(0, 100) : null,
                placeholder: el.placeholder || null,
                href: el.href || null,
                role: el.getAttribute('role') || null,
                ariaLabel: el.getAttribute('aria-label') || null,
                title: el.title || null,
                disabled: el.disabled || false,
                required: el.required || false,
                readonly: el.readOnly || false,
                visible: isVisible,
                position: {{
                    x: Math.round(rect.left),
                    y: Math.round(rect.top),
                    width: Math.round(rect.width),
                    height: Math.round(rect.height)
                }},
                index: i
            }};

            elements.push(elementInfo);
        }}

        return elements;
    }}
    """

    return await page.evaluate(interactive_script, max_elements, include_invisible)


async def _extract_forms_info() -> List[Dict]:
    """Extract form information."""
    global page

    forms_script = """
    () => {
        const forms = [];
        document.querySelectorAll('form').forEach((form, index) => {
            const formInfo = {
                id: form.id || null,
                class: form.className || null,
                action: form.action || null,
                method: form.method || 'get',
                name: form.name || null,
                index: index,
                fields: []
            };

            // Extract form fields
            form.querySelectorAll('input, select, textarea').forEach((field, fieldIndex) => {
                formInfo.fields.push({
                    tag: field.tagName.toLowerCase(),
                    type: field.type || null,
                    name: field.name || null,
                    id: field.id || null,
                    placeholder: field.placeholder || null,
                    required: field.required || false,
                    disabled: field.disabled || false,
                    value: field.value || null,
                    index: fieldIndex
                });
            });

            forms.push(formInfo);
        });
        return forms;
    }
    """

    return await page.evaluate(forms_script)


async def _extract_navigation_info() -> Dict:
    """Extract navigation information."""
    global page

    nav_script = """
    () => {
        const result = {
            menus: [],
            breadcrumbs: [],
            pagination: []
        };

        // Extract navigation menus
        document.querySelectorAll('nav, .nav, .menu, .navigation').forEach((nav, index) => {
            const links = Array.from(nav.querySelectorAll('a[href]')).map(a => ({
                text: a.textContent.trim(),
                href: a.href,
                active: a.classList.contains('active') || a.classList.contains('current')
            }));

            if (links.length > 0) {
                result.menus.push({
                    class: nav.className || null,
                    id: nav.id || null,
                    links: links,
                    index: index
                });
            }
        });

        // Extract breadcrumbs
        document.querySelectorAll('.breadcrumb, .breadcrumbs, [aria-label*="breadcrumb"]').forEach((bc, index) => {
            const items = Array.from(bc.querySelectorAll('a, span, li')).map(item => ({
                text: item.textContent.trim(),
                href: item.href || null,
                current: item.classList.contains('current') || item.getAttribute('aria-current') === 'page'
            }));

            if (items.length > 0) {
                result.breadcrumbs.push({
                    items: items,
                    index: index
                });
            }
        });

        // Extract pagination
        document.querySelectorAll('.pagination, .pager, .page-nav').forEach((pag, index) => {
            const links = Array.from(pag.querySelectorAll('a, button')).map(item => ({
                text: item.textContent.trim(),
                href: item.href || null,
                disabled: item.disabled || item.classList.contains('disabled'),
                current: item.classList.contains('current') || item.classList.contains('active')
            }));

            if (links.length > 0) {
                result.pagination.push({
                    links: links,
                    index: index
                });
            }
        });

        return result;
    }
    """

    return await page.evaluate(nav_script)


async def _extract_layout_info() -> Dict:
    """Extract layout and structure information."""
    global page

    layout_script = """
    () => {
        const result = {
            sections: [],
            containers: [],
            grid_items: []
        };

        // Extract main sections
        document.querySelectorAll('main, section, article, aside, header, footer').forEach((section, index) => {
            const rect = section.getBoundingClientRect();
            result.sections.push({
                tag: section.tagName.toLowerCase(),
                id: section.id || null,
                class: section.className || null,
                role: section.getAttribute('role') || null,
                position: {
                    x: Math.round(rect.left),
                    y: Math.round(rect.top),
                    width: Math.round(rect.width),
                    height: Math.round(rect.height)
                },
                index: index
            });
        });

        // Extract containers
        document.querySelectorAll('.container, .wrapper, .content, .main').forEach((container, index) => {
            const rect = container.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                result.containers.push({
                    class: container.className || null,
                    id: container.id || null,
                    position: {
                        x: Math.round(rect.left),
                        y: Math.round(rect.top),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    },
                    index: index
                });
            }
        });

        return result;
    }
    """

    return await page.evaluate(layout_script)


async def _extract_accessibility_info() -> Dict:
    """Extract accessibility information."""
    global page

    a11y_script = """
    () => {
        const result = {
            landmarks: [],
            headings_structure: [],
            form_labels: [],
            alt_texts: []
        };

        // Extract ARIA landmarks
        document.querySelectorAll('[role]').forEach((el, index) => {
            const role = el.getAttribute('role');
            if (['banner', 'navigation', 'main', 'complementary', 'contentinfo', 'search', 'form'].includes(role)) {
                result.landmarks.push({
                    role: role,
                    label: el.getAttribute('aria-label') || el.getAttribute('aria-labelledby') || null,
                    tag: el.tagName.toLowerCase(),
                    index: index
                });
            }
        });

        // Extract heading structure
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
        result.headings_structure = headings.map((h, index) => ({
            level: parseInt(h.tagName.charAt(1)),
            text: h.textContent.trim(),
            id: h.id || null,
            index: index
        }));

        // Extract form labels
        document.querySelectorAll('label').forEach((label, index) => {
            const forAttr = label.getAttribute('for');
            const associatedInput = forAttr ? document.getElementById(forAttr) : label.querySelector('input, select, textarea');

            result.form_labels.push({
                text: label.textContent.trim(),
                for: forAttr,
                associated_input: associatedInput ? {
                    tag: associatedInput.tagName.toLowerCase(),
                    type: associatedInput.type || null,
                    name: associatedInput.name || null
                } : null,
                index: index
            });
        });

        // Extract alt texts
        document.querySelectorAll('img').forEach((img, index) => {
            result.alt_texts.push({
                src: img.src,
                alt: img.alt || null,
                title: img.title || null,
                decorative: !img.alt && img.getAttribute('role') === 'presentation',
                index: index
            });
        });

        return result;
    }
    """

    return await page.evaluate(a11y_script)


async def _capture_element_screenshots() -> Dict:
    """Capture screenshots of key elements (optional feature)."""
    global page

    try:
        screenshots = {}

        # Take screenshot of main content area
        main_element = await page.query_selector('main, .main, .content, body')
        if main_element:
            screenshot_bytes = await main_element.screenshot()
            screenshots['main_content'] = {
                'data': screenshot_bytes.hex(),
                'format': 'png'
            }

        return screenshots
    except Exception as e:
        return {"error": f"Failed to capture screenshots: {str(e)}"}


@mcp.tool()
async def close_browser() -> str:
    """
    Close the browser instance.
    
    Returns:
        JSON string with close result
    """
    global playwright_instance, browser, page
    
    try:
        if page:
            await page.close()
            page = None

        if browser:
            await browser.close()
            browser = None

        if playwright_instance:
            await playwright_instance.stop()
            playwright_instance = None

        return json.dumps({"status": "success", "message": "Browser closed"}, indent=2)

    except Exception as e:
        error_result = {
            "status": "error",
            "error": str(e)
        }
        return json.dumps(error_result, indent=2)


if __name__ == "__main__":
    mcp.run()
