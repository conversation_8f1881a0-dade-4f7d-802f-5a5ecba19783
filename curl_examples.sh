#!/bin/bash
# 
# Curl examples for the simplified Web Brute Force API
#

echo "🎯 Web Brute Force API - Curl Examples"
echo "======================================"

# Check if API server is running
echo "🔍 Checking API server..."
if curl -s http://localhost:8000/ > /dev/null; then
    echo "✅ API server is running"
else
    echo "❌ API server is not running. Please start it first:"
    echo "   python start_services.py"
    exit 1
fi

echo ""
echo "📚 API Information:"
curl -s http://localhost:8000/ | python -m json.tool

echo ""
echo "🚀 Submitting a task with LLM-generated template:"

# Create a sample template
TEMPLATE='steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load

  fill_username:
    action: type
    target:
      selector: "input[name=\"username\"], #username"
    parameters:
      text: "{username}"

  fill_password:
    action: type
    target:
      selector: "input[name=\"password\"], #password"
    parameters:
      text: "{password}"

  submit_form:
    action: click
    target:
      selector: "button[type=\"submit\"], .login-btn"

  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
    failure:
      - ".error"
      - ".alert-danger"'

TASK_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/tasks" \
  -H "Content-Type: application/json" \
  -d "{\"url\": \"http://httpbin.org/status/200\", \"template\": \"$TEMPLATE\"}")

echo "$TASK_RESPONSE" | python -m json.tool

# Extract task ID
TASK_ID=$(echo "$TASK_RESPONSE" | python -c "import sys, json; print(json.load(sys.stdin)['task_id'])")
echo ""
echo "📋 Task ID: $TASK_ID"

echo ""
echo "⏳ Waiting 3 seconds for task to be processed..."
sleep 3

echo ""
echo "📊 Checking task status:"
curl -s "http://localhost:8000/api/tasks/$TASK_ID" | python -m json.tool

echo ""
echo "📋 Listing all tasks:"
curl -s "http://localhost:8000/api/tasks" | python -c "
import sys, json
tasks = json.load(sys.stdin)
print(f'Found {len(tasks)} tasks:')
for i, task in enumerate(tasks[:5], 1):
    status_emoji = {'pending': '⏳', 'running': '🔄', 'completed': '✅', 'failed': '❌', 'cancelled': '🚫'}.get(task['status'], '❓')
    print(f'  {i}. {status_emoji} {task[\"id\"][:8]}... - {task[\"status\"]} - {task[\"url\"]}')
"

echo ""
echo "🎉 Curl examples completed!"
echo ""
echo "💡 Key points:"
echo "  • URL and LLM-generated template are required"
echo "  • Template is validated and stored locally"
echo "  • Perfect for LLM integration workflow"
echo "  • Templates are automatically managed"

echo ""
echo "🔗 More examples:"
echo "  # Submit with different template"
echo "  curl -X POST http://localhost:8000/api/tasks -H 'Content-Type: application/json' -d '{\"url\": \"https://example.com/admin\", \"template\": \"your_yaml_here\"}'"
echo ""
echo "  # Monitor specific task"
echo "  curl http://localhost:8000/api/tasks/YOUR_TASK_ID"
echo ""
echo "  # List tasks with filtering"
echo "  curl 'http://localhost:8000/api/tasks?status=running&limit=10'"
