#!/usr/bin/env python3
"""
Simple MCP client test using direct JSON-RPC communication.
"""

import json
import subprocess
import sys
import time
from pathlib import Path


def test_mcp_direct():
    """Test MCP server with direct JSON-RPC calls."""
    print("🧪 Testing MCP Server with Direct JSON-RPC")
    print("=" * 50)
    
    # Start MCP server
    server_script = Path(__file__).parent / "brute_force_mcp_server.py"
    print(f"🚀 Starting MCP server: {server_script}")
    
    server_process = subprocess.Popen(
        [sys.executable, str(server_script)],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1
    )
    
    try:
        # Wait for server to start
        time.sleep(3)

        # Step 1: Send initialization request
        print("\n🔧 Initializing MCP connection...")
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }

        server_process.stdin.write(json.dumps(init_request) + "\n")
        server_process.stdin.flush()

        response_line = server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ Initialization response: {response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")

        # Step 2: Send initialized notification
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }

        server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        server_process.stdin.flush()

        # Step 3: List available tools
        print("\n1️⃣ Testing tools/list...")
        list_request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }

        server_process.stdin.write(json.dumps(list_request) + "\n")
        server_process.stdin.flush()

        response_line = server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ Tools list response received")
            if "result" in response and "tools" in response["result"]:
                tools = response["result"]["tools"]
                print(f"📚 Available tools: {len(tools)}")
                for tool in tools:
                    print(f"  🔧 {tool['name']}: {tool.get('description', 'No description')}")
            else:
                print(f"⚠️  Unexpected response format: {response}")
        else:
            print("❌ No response from server")
        
        # Step 4: Submit a task
        print("\n2️⃣ Testing submit_brute_force_task...")

        sample_template = """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password']"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit']"
  
  check_result:
    action: check
    success:
      - ".dashboard"
    failure:
      - ".error"
"""
        
        submit_request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "submit_brute_force_task",
                "arguments": {
                    "url": "http://httpbin.org/status/200",
                    "template": sample_template
                }
            }
        }
        
        server_process.stdin.write(json.dumps(submit_request) + "\n")
        server_process.stdin.flush()
        
        response_line = server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print(f"✅ Task submission response received")
            
            if "result" in response and "content" in response["result"]:
                result_content = json.loads(response["result"]["content"][0]["text"])
                if result_content["status"] == "success":
                    task_id = result_content["task_id"]
                    print(f"🎯 Task submitted successfully: {task_id}")
                    print(f"📝 Template saved: {result_content['configuration']['config_template']}")
                    
                    # Test 3: Get task status
                    print(f"\n3️⃣ Testing get_task_status...")
                    
                    status_request = {
                        "jsonrpc": "2.0",
                        "id": 4,
                        "method": "tools/call",
                        "params": {
                            "name": "get_task_status",
                            "arguments": {
                                "task_id": task_id
                            }
                        }
                    }
                    
                    server_process.stdin.write(json.dumps(status_request) + "\n")
                    server_process.stdin.flush()
                    
                    response_line = server_process.stdout.readline()
                    if response_line:
                        response = json.loads(response_line.strip())
                        if "result" in response and "content" in response["result"]:
                            status_content = json.loads(response["result"]["content"][0]["text"])
                            if status_content["status"] == "success":
                                task = status_content["task"]
                                print(f"✅ Task status retrieved")
                                print(f"📊 Status: {task['status']}")
                                print(f"📈 Progress: {task['current_attempts']}/{task['total_attempts']}")
                                print(f"🎯 Found credentials: {task['found_credentials']}")
                            else:
                                print(f"❌ Task status error: {status_content['error']}")
                        else:
                            print(f"⚠️  Unexpected status response: {response}")
                    else:
                        print("❌ No status response from server")
                        
                else:
                    print(f"❌ Task submission failed: {result_content['error']}")
            else:
                print(f"⚠️  Unexpected submission response: {response}")
        else:
            print("❌ No submission response from server")
        
        print("\n" + "=" * 50)
        print("🎉 MCP Direct Test Completed!")
        
    finally:
        print("\n🛑 Stopping MCP server...")
        server_process.terminate()
        server_process.wait()
        print("✅ MCP server stopped")


if __name__ == "__main__":
    test_mcp_direct()
