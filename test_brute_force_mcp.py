#!/usr/bin/env python3
"""
Test client for Brute Force MCP Server.
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path


class MCPClient:
    """Simple MCP client for testing."""
    
    def __init__(self, server_script):
        self.server_script = server_script
        self.server_process = None
    
    async def start_server(self):
        """Start the MCP server."""
        print("🚀 Starting MCP server...")
        self.server_process = subprocess.Popen(
            [sys.executable, str(self.server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for server to start
        await asyncio.sleep(2)
        print("✅ MCP server started")
    
    async def send_request(self, method: str, params: dict = None):
        """Send a request to the MCP server."""
        if not self.server_process:
            raise RuntimeError("Server not started")
        
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        request_json = json.dumps(request) + "\n"
        
        # Send request
        self.server_process.stdin.write(request_json)
        self.server_process.stdin.flush()
        
        # Read response
        response_line = self.server_process.stdout.readline()
        if response_line:
            return json.loads(response_line.strip())
        
        return None
    
    async def call_tool(self, tool_name: str, arguments: dict = None):
        """Call a specific tool."""
        params = {
            "name": tool_name,
            "arguments": arguments or {}
        }
        
        response = await self.send_request("tools/call", params)
        return response
    
    def stop_server(self):
        """Stop the MCP server."""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 MCP server stopped")


async def test_mcp_tools():
    """Test the MCP tools."""
    server_script = Path(__file__).parent / "brute_force_mcp_server.py"
    client = MCPClient(server_script)
    
    try:
        await client.start_server()
        
        print("\n🧪 Testing MCP Brute Force Tools")
        print("=" * 50)
        
        # Test 1: Submit a task
        print("\n1️⃣ Testing task submission...")
        
        sample_template = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], #username"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], #password"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], .login-btn"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
    failure:
      - ".error"
      - ".alert-danger"
"""
        
        response = await client.call_tool("submit_brute_force_task", {
            "url": "http://httpbin.org/status/200",
            "template": sample_template
        })
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "success":
                task_id = result["task_id"]
                print(f"✅ Task submitted: {task_id}")
                print(f"📋 Template saved: {result['configuration']['config_template']}")
            else:
                print(f"❌ Task submission failed: {result['error']}")
                return
        else:
            print(f"❌ No response from server")
            return
        
        # Test 2: Get task status
        print(f"\n2️⃣ Testing task status retrieval...")
        
        response = await client.call_tool("get_task_status", {
            "task_id": task_id
        })
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "success":
                task = result["task"]
                print(f"✅ Task status retrieved")
                print(f"📊 Status: {task['status']}")
                print(f"📈 Progress: {task['current_attempts']}/{task['total_attempts']}")
                print(f"🎯 Found credentials: {task['found_credentials']}")
            else:
                print(f"❌ Task status retrieval failed: {result['error']}")
        
        # Test 3: List tasks
        print(f"\n3️⃣ Testing task listing...")
        
        response = await client.call_tool("list_tasks", {
            "limit": 5
        })
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "success":
                tasks = result["tasks"]
                print(f"✅ Retrieved {len(tasks)} tasks")
                for i, task in enumerate(tasks[:3], 1):
                    print(f"  {i}. {task['id'][:8]}... - {task['status']} - {task['url']}")
            else:
                print(f"❌ Task listing failed: {result['error']}")
        
        # Test 4: Get task results
        print(f"\n4️⃣ Testing task results retrieval...")
        
        response = await client.call_tool("get_task_results", {
            "task_id": task_id
        })
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "success":
                print(f"✅ Task results retrieved")
                print(f"🎯 Found {result['summary']['total_credentials_found']} credentials")
                if result["results"]:
                    for cred in result["results"]:
                        print(f"  👤 {cred['username']}:{cred['password']}")
            else:
                print(f"❌ Task results retrieval failed: {result['error']}")
        
        # Test 5: Invalid operations
        print(f"\n5️⃣ Testing error handling...")
        
        # Test with invalid task ID
        response = await client.call_tool("get_task_status", {
            "task_id": "invalid-task-id"
        })
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "error":
                print(f"✅ Error handling works: {result['error']}")
            else:
                print(f"⚠️  Expected error but got success")
        
        print("\n" + "=" * 50)
        print("🎉 MCP tools testing completed!")
        
    finally:
        client.stop_server()


async def main():
    """Main test function."""
    print("🎯 Brute Force MCP Server Test Suite")
    print("=" * 50)
    
    # Ensure required files exist
    required_files = ["dicts/username.txt", "dicts/password.txt"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        print("Creating sample files...")
        
        # Create directories
        Path("dicts").mkdir(exist_ok=True)
        
        # Create sample files
        with open("dicts/username.txt", "w") as f:
            f.write("admin\ntest\nuser\n")
        
        with open("dicts/password.txt", "w") as f:
            f.write("admin\npassword\n123456\n")
        
        print("✅ Sample files created")
    
    await test_mcp_tools()


if __name__ == "__main__":
    asyncio.run(main())
