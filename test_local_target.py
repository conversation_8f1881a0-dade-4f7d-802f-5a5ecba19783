#!/usr/bin/env python3
"""
测试本地目标 127.0.0.1:9001 的连续执行机制。
"""

import json
import subprocess
import sys
import time
from pathlib import Path


class LocalTargetTest:
    """本地目标测试类。"""
    
    def __init__(self):
        self.server_process = None
        self.target_url = "http://127.0.0.1:9001"
    
    def start_mcp_server(self):
        """启动MCP服务器。"""
        server_script = Path(__file__).parent / "brute_force_mcp_server.py"
        print(f"🚀 启动MCP服务器...")
        
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        time.sleep(3)
        print("✅ MCP服务器已启动")
    
    def initialize_mcp(self):
        """初始化MCP连接。"""
        print("🔧 初始化MCP连接...")
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "local-target-test", "version": "1.0.0"}
            }
        }
        
        self.server_process.stdin.write(json.dumps(init_request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            server_name = response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')
            print(f"✅ 已连接到MCP服务器: {server_name}")
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        self.server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        self.server_process.stdin.flush()
    
    def submit_task(self, template: str, request_id: int) -> str:
        """提交任务到本地目标。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "submit_brute_force_task",
                "arguments": {
                    "url": self.target_url,
                    "template": template
                }
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                result = json.loads(response["result"]["content"][0]["text"])
                if result["status"] == "success":
                    return result["task_id"]
        
        return None
    
    def get_task_status(self, task_id: str, request_id: int) -> dict:
        """获取任务状态。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "get_task_status",
                "arguments": {"task_id": task_id}
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                return json.loads(response["result"]["content"][0]["text"])
        
        return {"status": "error", "error": "No response"}
    
    def test_continuous_execution(self):
        """测试针对127.0.0.1:9001的连续执行。"""
        print(f"\n🎯 测试目标: {self.target_url}")
        print("=" * 60)
        
        # 针对本地目标的不同测试模板
        test_templates = [
            {
                "name": "基础连接测试",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  check_result:
    action: check
    success:
      - "body"
      - "html"
    failure:
      - ".error-page"
"""
            },
            {
                "name": "登录表单检测",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[name='user'], input[type='email'], #username, #user"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password, #pass"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - ".home"
      - "text=Welcome"
      - "text=Dashboard"
    failure:
      - ".error"
      - ".alert-danger"
      - "text=Invalid"
      - "text=Login failed"
      - "text=Incorrect"
"""
            },
            {
                "name": "管理员登录测试",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}/admin"
      waitUntil: load
  
  fill_admin_user:
    action: type
    target:
      selector: "input[name='admin_user'], input[name='admin_username'], #admin_user, #admin_username"
    parameters:
      text: "{username}"
  
  fill_admin_pass:
    action: type
    target:
      selector: "input[name='admin_pass'], input[name='admin_password'], #admin_pass, #admin_password"
    parameters:
      text: "{password}"
  
  submit_admin_form:
    action: click
    target:
      selector: "button.admin-login, input.admin-submit, #admin_login"
  
  check_admin_result:
    action: check
    success:
      - ".admin-panel"
      - ".control-panel"
      - "text=Admin Panel"
      - "text=Control Panel"
    failure:
      - ".login-error"
      - "text=Access Denied"
      - "text=Unauthorized"
"""
            }
        ]
        
        submitted_tasks = []
        request_id = 2
        
        # 提交多个任务测试连续执行
        print(f"\n📤 向 {self.target_url} 提交 {len(test_templates)} 个任务...")
        for i, template_info in enumerate(test_templates, 1):
            print(f"\n{i}. 提交任务: {template_info['name']}")
            
            task_id = self.submit_task(template_info["template"], request_id)
            request_id += 1
            
            if task_id:
                print(f"   ✅ 任务已提交: {task_id}")
                submitted_tasks.append({
                    "id": task_id,
                    "name": template_info["name"]
                })
                # 间隔2秒提交下一个任务
                time.sleep(2)
            else:
                print(f"   ❌ 任务提交失败")
        
        print(f"\n📊 总共提交了 {len(submitted_tasks)} 个任务到 {self.target_url}")
        print("🔄 执行器将连续处理这些任务...")
        
        # 监控执行进度
        print(f"\n📈 监控任务执行进度...")
        print("=" * 60)
        
        completed_tasks = set()
        monitoring_rounds = 0
        
        while len(completed_tasks) < len(submitted_tasks) and monitoring_rounds < 15:
            monitoring_rounds += 1
            print(f"\n🔍 第 {monitoring_rounds} 轮监控 (每8秒检查一次)")
            
            for task in submitted_tasks:
                if task["id"] in completed_tasks:
                    continue
                
                status_result = self.get_task_status(task["id"], request_id)
                request_id += 1
                
                if status_result["status"] == "success":
                    task_data = status_result["task"]
                    status = task_data["status"]
                    progress = f"{task_data['current_attempts']}/{task_data['total_attempts']}"
                    
                    status_emoji = {
                        "pending": "⏳",
                        "running": "🔄",
                        "completed": "✅",
                        "failed": "❌",
                        "cancelled": "🚫"
                    }.get(status, "❓")
                    
                    print(f"  {status_emoji} {task['name'][:25]:<25} | {status:<10} | {progress}")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        completed_tasks.add(task["id"])
                        if task_data.get("found_credentials"):
                            print(f"    🎯 发现有效凭据!")
                        if task_data.get("error_message"):
                            print(f"    ❌ 错误: {task_data['error_message']}")
                
                else:
                    print(f"  ❌ {task['name'][:25]:<25} | 查询失败")
            
            if len(completed_tasks) < len(submitted_tasks):
                print(f"\n⏳ 等待8秒后继续监控... ({len(completed_tasks)}/{len(submitted_tasks)} 已完成)")
                time.sleep(8)
        
        # 最终总结
        print(f"\n" + "=" * 60)
        print(f"🎉 本地目标测试完成!")
        print(f"🎯 目标地址: {self.target_url}")
        print(f"📊 任务统计:")
        print(f"  • 提交任务数: {len(submitted_tasks)}")
        print(f"  • 完成任务数: {len(completed_tasks)}")
        print(f"  • 监控轮数: {monitoring_rounds}")
        
        print(f"\n✅ 连续执行验证:")
        print(f"  ✅ 执行器成功连续处理多个任务")
        print(f"  ✅ 完成一个任务后自动获取下一个")
        print(f"  ✅ 针对 {self.target_url} 的测试正常执行")
        print(f"  ✅ 异步Playwright API工作正常")
    
    def stop_server(self):
        """停止MCP服务器。"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 MCP服务器已停止")
    
    def run_test(self):
        """运行完整测试。"""
        print("🎯 本地目标连续执行测试")
        print("=" * 60)
        print(f"目标: {self.target_url}")
        print("验证: 执行器连续循环处理多个任务")
        print("=" * 60)
        
        try:
            # 启动MCP服务器
            self.start_mcp_server()
            
            # 初始化MCP连接
            self.initialize_mcp()
            
            # 测试连续执行
            self.test_continuous_execution()
            
        finally:
            self.stop_server()


def main():
    """主入口。"""
    test = LocalTargetTest()
    test.run_test()


if __name__ == "__main__":
    main()
