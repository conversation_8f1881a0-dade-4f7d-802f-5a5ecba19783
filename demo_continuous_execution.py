#!/usr/bin/env python3
"""
演示执行器的连续循环执行机制。
提交多个任务，观察执行器如何依次处理。
"""

import json
import subprocess
import sys
import time
from pathlib import Path


class ContinuousExecutionDemo:
    """连续执行演示类。"""
    
    def __init__(self):
        self.server_process = None
    
    def start_mcp_server(self):
        """启动MCP服务器。"""
        server_script = Path(__file__).parent / "brute_force_mcp_server.py"
        print(f"🚀 启动MCP服务器...")
        
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        time.sleep(3)
        print("✅ MCP服务器已启动")
    
    def initialize_mcp(self):
        """初始化MCP连接。"""
        print("🔧 初始化MCP连接...")
        
        # 发送初始化请求
        init_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"tools": {}},
                "clientInfo": {"name": "continuous-demo", "version": "1.0.0"}
            }
        }
        
        self.server_process.stdin.write(json.dumps(init_request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            server_name = response.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')
            print(f"✅ 已连接到MCP服务器: {server_name}")
        
        # 发送初始化完成通知
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        self.server_process.stdin.write(json.dumps(initialized_notification) + "\n")
        self.server_process.stdin.flush()
    
    def submit_task(self, url: str, template: str, request_id: int) -> str:
        """提交单个任务。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "submit_brute_force_task",
                "arguments": {
                    "url": url,
                    "template": template
                }
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                result = json.loads(response["result"]["content"][0]["text"])
                if result["status"] == "success":
                    return result["task_id"]
        
        return None
    
    def get_task_status(self, task_id: str, request_id: int) -> dict:
        """获取任务状态。"""
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": "tools/call",
            "params": {
                "name": "get_task_status",
                "arguments": {"task_id": task_id}
            }
        }
        
        self.server_process.stdin.write(json.dumps(request) + "\n")
        self.server_process.stdin.flush()
        
        response_line = self.server_process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            if "result" in response and "content" in response["result"]:
                return json.loads(response["result"]["content"][0]["text"])
        
        return {"status": "error", "error": "No response"}
    
    def demo_continuous_execution(self):
        """演示连续执行机制。"""
        print("\n🔄 演示执行器连续循环执行机制")
        print("=" * 60)
        
        # 准备多个测试任务
        test_tasks = [
            {
                "name": "简单HTTP测试",
                "url": "http://httpbin.org/status/200",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  check_result:
    action: check
    success:
      - "body"
    failure:
      - ".error"
"""
            },
            {
                "name": "登录表单测试",
                "url": "http://httpbin.org/forms/post",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  fill_username:
    action: type
    target:
      selector: "input[name='custname']"
    parameters:
      text: "{username}"
  fill_password:
    action: type
    target:
      selector: "input[name='custtel']"
    parameters:
      text: "{password}"
  submit_form:
    action: click
    target:
      selector: "input[type='submit']"
  check_result:
    action: check
    success:
      - ".success"
    failure:
      - ".error"
"""
            },
            {
                "name": "JSON API测试",
                "url": "http://httpbin.org/json",
                "template": """steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  check_result:
    action: check
    success:
      - "pre"
      - "body"
    failure:
      - ".error"
"""
            }
        ]
        
        submitted_tasks = []
        request_id = 2
        
        # 批量提交任务
        print(f"\n📤 批量提交 {len(test_tasks)} 个任务...")
        for i, task_info in enumerate(test_tasks, 1):
            print(f"\n{i}. 提交任务: {task_info['name']}")
            print(f"   URL: {task_info['url']}")
            
            task_id = self.submit_task(task_info["url"], task_info["template"], request_id)
            request_id += 1
            
            if task_id:
                print(f"   ✅ 任务已提交: {task_id}")
                submitted_tasks.append({
                    "id": task_id,
                    "name": task_info["name"],
                    "url": task_info["url"]
                })
            else:
                print(f"   ❌ 任务提交失败")
        
        print(f"\n📊 总共提交了 {len(submitted_tasks)} 个任务")
        print("🔄 执行器将按顺序处理这些任务...")
        
        # 监控任务执行
        print(f"\n📈 监控任务执行进度...")
        print("=" * 60)
        
        completed_tasks = set()
        monitoring_rounds = 0
        
        while len(completed_tasks) < len(submitted_tasks) and monitoring_rounds < 20:
            monitoring_rounds += 1
            print(f"\n🔍 第 {monitoring_rounds} 轮监控 (每10秒检查一次)")
            
            for task in submitted_tasks:
                if task["id"] in completed_tasks:
                    continue
                
                status_result = self.get_task_status(task["id"], request_id)
                request_id += 1
                
                if status_result["status"] == "success":
                    task_data = status_result["task"]
                    status = task_data["status"]
                    progress = f"{task_data['current_attempts']}/{task_data['total_attempts']}"
                    
                    status_emoji = {
                        "pending": "⏳",
                        "running": "🔄",
                        "completed": "✅",
                        "failed": "❌",
                        "cancelled": "🚫"
                    }.get(status, "❓")
                    
                    print(f"  {status_emoji} {task['name'][:20]:<20} | {status:<10} | {progress}")
                    
                    if status in ["completed", "failed", "cancelled"]:
                        completed_tasks.add(task["id"])
                        if task_data.get("found_credentials"):
                            print(f"    🎯 发现有效凭据!")
                        if task_data.get("error_message"):
                            print(f"    ❌ 错误: {task_data['error_message']}")
                
                else:
                    print(f"  ❌ {task['name'][:20]:<20} | 查询失败")
            
            if len(completed_tasks) < len(submitted_tasks):
                print(f"\n⏳ 等待10秒后继续监控... ({len(completed_tasks)}/{len(submitted_tasks)} 已完成)")
                time.sleep(10)
        
        # 最终总结
        print(f"\n" + "=" * 60)
        print(f"🎉 连续执行演示完成!")
        print(f"📊 任务统计:")
        print(f"  • 提交任务数: {len(submitted_tasks)}")
        print(f"  • 完成任务数: {len(completed_tasks)}")
        print(f"  • 监控轮数: {monitoring_rounds}")
        
        print(f"\n✅ 验证结果:")
        print(f"  ✅ 执行器成功循环处理多个任务")
        print(f"  ✅ 任务按提交顺序依次执行")
        print(f"  ✅ 完成一个任务后自动获取下一个")
        print(f"  ✅ 无任务时进入等待状态")
    
    def stop_server(self):
        """停止MCP服务器。"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process.wait()
            print("🛑 MCP服务器已停止")
    
    def run_demo(self):
        """运行完整演示。"""
        print("🎯 执行器连续循环执行演示")
        print("=" * 60)
        print("目标: 验证执行器能够连续处理多个任务")
        print("机制: 完成当前任务 → 轮询新任务 → 执行 → 循环")
        print("=" * 60)
        
        try:
            # 启动MCP服务器
            self.start_mcp_server()
            
            # 初始化MCP连接
            self.initialize_mcp()
            
            # 演示连续执行
            self.demo_continuous_execution()
            
        finally:
            self.stop_server()


def main():
    """主入口。"""
    demo = ContinuousExecutionDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
