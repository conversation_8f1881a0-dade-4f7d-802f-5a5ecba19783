#!/usr/bin/env python3
"""
Demo script showing the complete MCP workflow for brute force testing.
Simulates LLM → MCP → Executor workflow.
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path


class BruteForceMCPDemo:
    """Demo class for MCP workflow."""
    
    def __init__(self):
        self.mcp_server_process = None
        self.executor_process = None
    
    async def start_mcp_server(self):
        """Start the MCP server."""
        print("🚀 Starting Brute Force MCP Server...")
        server_script = Path(__file__).parent / "brute_force_mcp_server.py"
        
        self.mcp_server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        await asyncio.sleep(2)
        print("✅ MCP server started")
    
    def start_executor_service(self):
        """Start the executor service."""
        print("⚙️  Starting Executor Service...")
        executor_script = Path(__file__).parent / "brute_force_executor.py"
        
        self.executor_process = subprocess.Popen(
            [sys.executable, str(executor_script)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        time.sleep(2)
        print("✅ Executor service started")
    
    async def send_mcp_request(self, method: str, params: dict = None):
        """Send a request to the MCP server."""
        if not self.mcp_server_process:
            raise RuntimeError("MCP server not started")
        
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params or {}
        }
        
        request_json = json.dumps(request) + "\n"
        
        # Send request
        self.mcp_server_process.stdin.write(request_json)
        self.mcp_server_process.stdin.flush()
        
        # Read response
        response_line = self.mcp_server_process.stdout.readline()
        if response_line:
            return json.loads(response_line.strip())
        
        return None
    
    async def call_mcp_tool(self, tool_name: str, arguments: dict = None):
        """Call a specific MCP tool."""
        params = {
            "name": tool_name,
            "arguments": arguments or {}
        }
        
        response = await self.send_mcp_request("tools/call", params)
        return response
    
    def simulate_llm_analysis(self, url: str) -> str:
        """Simulate LLM analyzing URL and generating template."""
        print(f"🤖 [Simulated LLM] Analyzing URL: {url}")
        print("🤖 [Simulated LLM] Detecting login forms...")
        print("🤖 [Simulated LLM] Generating execution template...")
        
        # Simulate different templates based on URL
        if "admin" in url.lower():
            template = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_admin_user:
    action: type
    target:
      selector: "#admin_user, input[name='admin_username'], .admin-login input[type='text']"
    parameters:
      text: "{username}"
  
  fill_admin_pass:
    action: type
    target:
      selector: "#admin_pass, input[name='admin_password'], .admin-login input[type='password']"
    parameters:
      text: "{password}"
  
  click_admin_login:
    action: click
    target:
      selector: "#admin_login, .admin-submit, button.admin-btn"
  
  check_admin_result:
    action: check
    success:
      - ".admin-dashboard"
      - ".control-panel"
      - "text=Admin Panel"
    failure:
      - ".login-error"
      - "text=Access Denied"
"""
        else:
            template = """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='email'], #username, #user"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password, #pass"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - ".home"
      - "text=Welcome"
    failure:
      - ".error"
      - ".alert-danger"
      - "text=Invalid"
      - "text=Login failed"
"""
        
        print("🤖 [Simulated LLM] Template generated successfully!")
        return template.strip()
    
    async def demonstrate_workflow(self):
        """Demonstrate the complete MCP workflow."""
        print("\n🎯 Demonstrating Complete MCP Workflow")
        print("=" * 60)
        
        # Step 1: Simulate LLM analysis
        test_urls = [
            "http://httpbin.org/status/200",
            "https://example.com/admin/login",
            "http://test.com/user/login"
        ]
        
        submitted_tasks = []
        
        for url in test_urls:
            print(f"\n📋 Processing URL: {url}")
            
            # Step 1: LLM analyzes and generates template
            template = self.simulate_llm_analysis(url)
            
            # Step 2: Submit via MCP
            print(f"📡 [MCP] Submitting task via MCP tools...")
            response = await self.call_mcp_tool("submit_brute_force_task", {
                "url": url,
                "template": template
            })
            
            if response and "result" in response:
                result = json.loads(response["result"])
                if result["status"] == "success":
                    task_id = result["task_id"]
                    print(f"✅ [MCP] Task submitted: {task_id}")
                    print(f"📝 [MCP] Template saved: {result['configuration']['config_template']}")
                    submitted_tasks.append(task_id)
                else:
                    print(f"❌ [MCP] Task submission failed: {result['error']}")
        
        # Step 3: Monitor tasks
        if submitted_tasks:
            print(f"\n📊 Monitoring {len(submitted_tasks)} tasks...")
            
            for task_id in submitted_tasks:
                print(f"\n🔍 [MCP] Checking task: {task_id[:8]}...")
                
                response = await self.call_mcp_tool("get_task_status", {
                    "task_id": task_id
                })
                
                if response and "result" in response:
                    result = json.loads(response["result"])
                    if result["status"] == "success":
                        task = result["task"]
                        print(f"  📈 Status: {task['status']}")
                        print(f"  📊 Progress: {task['current_attempts']}/{task['total_attempts']}")
                        print(f"  🎯 Found credentials: {task['found_credentials']}")
                        
                        if task['error_message']:
                            print(f"  ❌ Error: {task['error_message']}")
        
        # Step 4: List all tasks
        print(f"\n📋 [MCP] Listing all tasks...")
        response = await self.call_mcp_tool("list_tasks", {"limit": 10})
        
        if response and "result" in response:
            result = json.loads(response["result"])
            if result["status"] == "success":
                tasks = result["tasks"]
                print(f"✅ [MCP] Found {len(tasks)} tasks")
                
                for i, task in enumerate(tasks[:5], 1):
                    status_emoji = {
                        "pending": "⏳",
                        "running": "🔄", 
                        "completed": "✅",
                        "failed": "❌",
                        "cancelled": "🚫"
                    }.get(task['status'], "❓")
                    
                    print(f"  {i}. {status_emoji} {task['id'][:8]}... - {task['status']} - {task['url']}")
    
    def stop_services(self):
        """Stop all services."""
        print("\n🛑 Stopping services...")
        
        if self.mcp_server_process:
            self.mcp_server_process.terminate()
            self.mcp_server_process.wait()
            print("✅ MCP server stopped")
        
        if self.executor_process:
            self.executor_process.terminate()
            self.executor_process.wait()
            print("✅ Executor service stopped")
    
    async def run_demo(self):
        """Run the complete demo."""
        try:
            # Start services
            await self.start_mcp_server()
            self.start_executor_service()
            
            # Wait for services to be ready
            print("\n⏳ Waiting for services to be ready...")
            await asyncio.sleep(3)
            
            # Demonstrate workflow
            await self.demonstrate_workflow()
            
            print("\n" + "=" * 60)
            print("🎉 MCP Workflow Demo Completed!")
            print("\n📚 What was demonstrated:")
            print("  ✅ LLM template generation simulation")
            print("  ✅ MCP tool calls for task submission")
            print("  ✅ Template storage and validation")
            print("  ✅ Task monitoring via MCP")
            print("  ✅ Executor service integration")
            
            print(f"\n💡 Next steps:")
            print(f"  • Integrate with real LLM for template generation")
            print(f"  • Configure Claude Desktop with MCP")
            print(f"  • Use MCP tools in your applications")
            
        finally:
            self.stop_services()


async def main():
    """Main entry point."""
    demo = BruteForceMCPDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
