
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: networkidle
  
  type_username:
    action: type
    target:
      selector: "input[type='text']:first, input[name*='user']:first"
    parameters:
      text: "{username}"
  
  type_password:
    action: type
    target:
      selector: "input[type='password']:first"
    parameters:
      text: "{password}"
  
  submit:
    action: click
    target:
      selector: "form button, form input[type='submit']"
  
  verify:
    action: check
    success:
      - "body:not(:contains('login'))"
      - ".success"
    failure:
      - ".error"
      - "text=failed"
