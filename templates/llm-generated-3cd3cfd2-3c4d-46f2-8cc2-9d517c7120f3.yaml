steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password']"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit']"
  
  check_result:
    action: check
    success:
      - ".dashboard"
    failure:
      - ".error"
