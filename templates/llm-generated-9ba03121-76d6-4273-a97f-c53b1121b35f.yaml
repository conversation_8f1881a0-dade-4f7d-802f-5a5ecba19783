# Playwright Automation Flow for MinIO Console Login
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition for the brute-force attack.
# The loop will stop if these conditions are met.
failure_condition:
  any:
    # Failure condition: 401 Unauthorized error or error message appears
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    - type: element_is_visible
      target:
        type: text
        value: "Unauthorized"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."

  - name: "Wait for Response"
    action: "wait"
    parameters:
      timeout: 3000
    description: "Wait for the login response to be processed."

  - name: "Check Login Result"
    action: "check"
    success:
      - type: "url_contains"
        value: "/dashboard"
      - type: "url_contains" 
        value: "/buckets"
    failure:
      - type: "element_is_visible"
        target:
          type: "text"
          value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
      - type: "console_error"
        value: "401"
    description: "Check if login was successful by looking for dashboard URL or failure indicators."