steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='text']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password']"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit']"
  
  check_result:
    action: check
    success:
      - ".authenticated"
      - "text=authenticated"
    failure:
      - ".error"
      - "text=Unauthorized"
