
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load

  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='email'], #username"
    parameters:
      text: "{username}"

  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password"
    parameters:
      text: "{password}"

  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn"

  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
    failure:
      - ".error"
      - ".alert-danger"
