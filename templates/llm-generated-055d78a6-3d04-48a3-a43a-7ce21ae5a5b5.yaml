steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[name='user'], input[type='text'], #username, #user"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password, #pass"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login, .btn-login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - ".home"
      - ".success"
      - "text=Welcome"
      - "text=Dashboard"
      - "text=Success"
    failure:
      - ".error"
      - ".alert-danger"
      - ".login-error"
      - "text=Invalid"
      - "text=Login failed"
      - "text=Error"
