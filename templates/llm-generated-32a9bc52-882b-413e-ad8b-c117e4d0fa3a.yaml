
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "#admin_user, input[name='admin_username'], .admin-login input[type='text']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "#admin_pass, input[name='admin_password'], .admin-login input[type='password']"
    parameters:
      text: "{password}"
  
  click_login:
    action: click
    target:
      selector: "#admin_login, .admin-submit, button.admin-btn"
  
  check_result:
    action: check
    success:
      - ".admin-dashboard"
      - ".control-panel"
      - "text=Admin Panel"
    failure:
      - ".login-error"
      - "text=Access Denied"
      - "text=Invalid credentials"
