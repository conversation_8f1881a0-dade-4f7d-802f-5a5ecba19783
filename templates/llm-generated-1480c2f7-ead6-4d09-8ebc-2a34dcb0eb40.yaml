# Playwright Automation Flow for MinIO Console Login (Based on Actual Observations)
name: MinIO Console Login Template - Corrected
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001, based on actual test observations.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition based on ACTUAL observations during testing
# Success is determined by the ABSENCE of these failure indicators
failure_condition:
  any:
    # Failure condition 1: Specific error message appears (actually observed)
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    # Failure condition 2: Still on login page after attempt (URL unchanged)
    - type: url_equals
      value: "http://localhost:9001/login"
    # Failure condition 3: Login page title still present
    - type: page_title_equals
      value: "MinIO Console"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."

  - name: "Wait for Response"
    action: "wait"
    parameters:
      timeout: 3000
    description: "Wait for the login response to be processed."

  - name: "Check Login Result"
    action: "check"
    # NOTE: No success indicators defined because we haven't observed successful login
    # Success will be determined by the ABSENCE of failure conditions
    success: []
    failure:
      # Only include failure indicators that were ACTUALLY observed during testing
      - type: "element_is_visible"
        target:
          type: "text"
          value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
      - type: "url_equals"
        value: "http://localhost:9001/login"
    description: "Check if login failed by looking for observed failure indicators. Success is determined by absence of these indicators."