# MinIO Console 登录测试模板使用手册

## 概述

`test-minio-console.yaml` 是一个专门用于 MinIO Console 登录页面的 Playwright 自动化测试模板。该模板可以与 `grafana_executor.py` 配合使用，进行自动化的登录测试和暴力破解攻击。

## 模板信息

- **文件名**: `test-minio-console.yaml`
- **目标应用**: MinIO Console
- **默认URL**: `http://localhost:9001/login`
- **支持操作**: 用户名/密码登录测试

## 前置条件

### 1. 环境要求
- Python 3.7+
- Playwright 库已安装
- PyYAML 库已安装
- requests 库已安装

### 2. 目标服务
- MinIO Console 服务运行在 `localhost:9001`
- 登录页面可正常访问

### 3. 字典文件
- `dicts/username.txt` - 用户名字典
- `dicts/password.txt` - 密码字典

## 模板结构说明

### 基本配置
```yaml
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001.
```

### 变量定义
```yaml
variables:
  - username  # 从字典文件中读取的用户名
  - password  # 从字典文件中读取的密码
```

### 失败条件
```yaml
failure_condition:
  any:
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
```

**说明**: 当页面出现此错误消息时，表示登录失败。

### 操作步骤
1. **导航到登录页面**
2. **填写用户名**
3. **填写密码**  
4. **点击登录按钮**
5. **等待响应**

## 使用方法

### 1. 基本使用

```bash
python grafana_executor.py
```

**注意**: 需要修改 `grafana_executor.py` 中的配置文件路径：
```python
config_file_path = "templates/test-minio-console.yaml"
```

### 2. 准备字典文件

创建用户名字典 (`dicts/username.txt`):
```
admin
minio
root
administrator
user
test
```

创建密码字典 (`dicts/password.txt`):
```
password
123456
admin
minio123
password123
12345678
```

### 3. 执行测试

```bash
# 确保目标服务运行
curl http://localhost:9001/login

# 执行暴力破解
python grafana_executor.py
```

## 定位器说明

### 用户名输入框
```yaml
target:
  type: "role"
  value: "textbox"
  options:
    name: "Username"
```
- **定位方式**: 基于角色的定位器
- **Playwright代码**: `page.getByRole('textbox', { name: 'Username' })`

### 密码输入框
```yaml
target:
  type: "role"
  value: "textbox"
  options:
    name: "Password"
```
- **定位方式**: 基于角色的定位器
- **Playwright代码**: `page.getByRole('textbox', { name: 'Password' })`

### 登录按钮
```yaml
target:
  type: "role"
  value: "button"
  options:
    name: "Login"
```
- **定位方式**: 基于角色的定位器
- **Playwright代码**: `page.getByRole('button', { name: 'Login' })`

## 成功/失败判断

### 失败标识
- **HTTP状态码**: 401 Unauthorized
- **错误消息**: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
- **页面行为**: 保持在登录页面

### 成功标识
- **页面跳转**: 跳转到 MinIO Console 主界面
- **URL变化**: 从 `/login` 跳转到 `/` 或其他管理页面
- **无错误消息**: 不出现失败条件中的错误文本

## 自定义配置

### 修改目标URL
```yaml
parameters:
  url: "http://your-minio-server:9000/login"  # 修改为实际地址
```

### 调整等待时间
```yaml
- name: "Wait for Response"
  action: "wait"
  parameters:
    time: 5  # 增加等待时间（秒）
```

### 添加额外检查步骤
```yaml
- name: "Check Dashboard"
  action: "wait_for_url"
  parameters:
    url_pattern: "*/dashboard*"
    timeout: 5000
  description: "Wait for successful redirect to dashboard"
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查 MinIO Console 服务是否运行
   - 验证端口 9001 是否正确

2. **元素定位失败**
   - 检查页面是否完全加载
   - 验证元素的 name 属性是否正确

3. **登录按钮不可点击**
   - 确保用户名和密码都已填写
   - 检查表单验证逻辑

### 调试模式

修改 `grafana_executor.py` 启用可视化模式：
```python
browser = p.chromium.launch(headless=False)  # 设置为 False
```

## 日志输出

执行结果会记录在 `login_attempts.log` 文件中：
```
[2024-01-01 12:00:00] Username: admin, Password: password, Result: FAILURE
  Details: Failure: Failure text 'Expected element type <AssumeRoleResponse> but have <ErrorResponse>' IS visible. Login failed.

[2024-01-01 12:00:05] Username: minio, Password: minio123, Result: SUCCESS
  Details: Success: Failure text 'Expected element type <AssumeRoleResponse> but have <ErrorResponse>' is NOT visible after timeout. Login likely successful.
```

## 安全注意事项

1. **仅用于授权测试**: 只在获得明确授权的系统上使用
2. **控制测试频率**: 避免对目标系统造成过大负载
3. **保护敏感信息**: 及时清理日志文件中的凭据信息
4. **遵守法律法规**: 确保测试活动符合相关法律要求

## 扩展功能

### 添加成功条件检测
可以在模板中添加 `success_condition` 来更精确地判断登录成功：

```yaml
success_condition:
  any:
    - type: url_contains
      value: "/dashboard"
    - type: element_is_visible
      target:
        type: text
        value: "Welcome to MinIO Console"
```

### 支持多种登录方式
MinIO Console 支持 STS 登录，可以扩展模板支持：

```yaml
- name: "Click Use STS"
  action: "click"
  target:
    type: "text"
    value: "Use STS"
  condition: "{login_method} == 'sts'"
```
