#!/usr/bin/env python3
"""
Run the Brute Force MCP server.
"""

import asyncio
import os
import sys
from pathlib import Path
from brute_force_mcp_server import mcp


def check_dependencies():
    """Check if all required files exist."""
    required_dirs = ["dicts", "templates"]
    required_files = [
        "dicts/username.txt",
        "dicts/password.txt"
    ]
    
    print("🔍 Checking dependencies...")
    
    # Check directories
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Missing directory: {directory}")
            return False
        else:
            print(f"✅ Directory found: {directory}")
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ Missing file: {file_path}")
            return False
        else:
            print(f"✅ File found: {file_path}")
    
    return True


def create_sample_files():
    """Create sample dictionary files if they don't exist."""
    print("📝 Creating sample files...")
    
    # Ensure directories exist
    os.makedirs("dicts", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    
    # Create sample username dictionary
    username_file = "dicts/username.txt"
    if not os.path.exists(username_file):
        with open(username_file, 'w', encoding='utf-8') as f:
            f.write("admin\n")
            f.write("administrator\n")
            f.write("root\n")
            f.write("user\n")
            f.write("test\n")
            f.write("guest\n")
        print(f"✅ Created sample file: {username_file}")
    
    # Create sample password dictionary
    password_file = "dicts/password.txt"
    if not os.path.exists(password_file):
        with open(password_file, 'w', encoding='utf-8') as f:
            f.write("admin\n")
            f.write("password\n")
            f.write("123456\n")
            f.write("admin123\n")
            f.write("root\n")
            f.write("test\n")
            f.write("guest\n")
        print(f"✅ Created sample file: {password_file}")


async def main():
    """Main entry point."""
    print("🎯 Brute Force MCP Server")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n⚠️  Some dependencies are missing.")
        print("Creating sample files...")
        create_sample_files()
        
        # Check again
        if not check_dependencies():
            print("❌ Failed to create required files. Please check permissions.")
            sys.exit(1)
    
    print("\n✅ All dependencies satisfied!")
    print("=" * 40)
    
    print("🚀 Starting Brute Force MCP Server...")
    print("\n📚 Available MCP Tools:")
    print("  🔧 submit_brute_force_task - Submit URL with LLM template")
    print("  📊 get_task_status - Get task progress and details")
    print("  📋 list_tasks - List all tasks with filtering")
    print("  🚫 cancel_task - Cancel running task")
    print("  🎯 get_task_results - Get successful credentials")
    
    print("\n💡 Usage with MCP clients:")
    print("  • Claude Desktop with MCP configuration")
    print("  • Custom MCP clients")
    print("  • Command line MCP tools")
    
    print("\n🔗 Integration with executor service:")
    print("  • Start executor service separately: python brute_force_executor.py")
    print("  • Or use service manager: python start_services.py")
    
    print("\n" + "=" * 40)
    print("📡 MCP Server is ready to accept connections...")
    print("Press Ctrl+C to stop the server")
    print("=" * 40)
    
    try:
        # Run the server
        await mcp.run()
    except KeyboardInterrupt:
        print("\n👋 MCP server stopped by user")
    except Exception as e:
        print(f"\n❌ MCP server error: {e}")
        sys.exit(1)


def run_sync():
    """Run the server synchronously."""
    print("🎯 Brute Force MCP Server")
    print("=" * 40)

    # Check dependencies
    if not check_dependencies():
        print("\n⚠️  Some dependencies are missing.")
        print("Creating sample files...")
        create_sample_files()

        # Check again
        if not check_dependencies():
            print("❌ Failed to create required files. Please check permissions.")
            sys.exit(1)

    print("\n✅ All dependencies satisfied!")
    print("=" * 40)

    print("🚀 Starting Brute Force MCP Server...")
    print("\n📚 Available MCP Tools:")
    print("  🔧 submit_brute_force_task - Submit URL with LLM template")
    print("  📊 get_task_status - Get task progress and details")
    print("  📋 list_tasks - List all tasks with filtering")
    print("  🚫 cancel_task - Cancel running task")
    print("  🎯 get_task_results - Get successful credentials")

    print("\n💡 Usage with MCP clients:")
    print("  • Claude Desktop with MCP configuration")
    print("  • Custom MCP clients")
    print("  • Command line MCP tools")

    print("\n🔗 Integration with executor service:")
    print("  • Start executor service separately: python brute_force_executor.py")
    print("  • Or use service manager: python start_services.py")

    print("\n" + "=" * 40)
    print("📡 MCP Server is ready to accept connections...")
    print("Press Ctrl+C to stop the server")
    print("=" * 40)

    # Run the MCP server directly
    mcp.run()


if __name__ == "__main__":
    run_sync()
