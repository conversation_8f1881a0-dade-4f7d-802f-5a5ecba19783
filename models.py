#!/usr/bin/env python3
"""
Database models for web brute force API server.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, List
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer, <PERSON><PERSON>an, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship

Base = declarative_base()


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class BruteForceTask(Base):
    """Main task table for storing brute force jobs."""
    __tablename__ = "brute_force_tasks"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    url = Column(String, nullable=False, index=True)
    status = Column(String, nullable=False, default=TaskStatus.PENDING.value, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Configuration
    username_dict_path = Column(String, nullable=True)
    password_dict_path = Column(String, nullable=True)
    config_template = Column(String, nullable=True)  # YAML template to use
    
    # Progress tracking
    total_attempts = Column(Integer, default=0)
    current_attempts = Column(Integer, default=0)
    successful_attempts = Column(Integer, default=0)
    
    # Results
    error_message = Column(Text, nullable=True)
    found_credentials = Column(Boolean, default=False)
    
    # Relationships
    results = relationship("BruteForceResult", back_populates="task", cascade="all, delete-orphan")
    logs = relationship("TaskLog", back_populates="task", cascade="all, delete-orphan")


class BruteForceResult(Base):
    """Table for storing successful brute force results."""
    __tablename__ = "brute_force_results"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    task_id = Column(String, ForeignKey("brute_force_tasks.id"), nullable=False, index=True)
    username = Column(String, nullable=False)
    password = Column(String, nullable=False)
    discovered_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    response_details = Column(Text, nullable=True)  # JSON string with response details
    
    # Relationships
    task = relationship("BruteForceTask", back_populates="results")


class TaskLog(Base):
    """Table for storing detailed task execution logs."""
    __tablename__ = "task_logs"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    task_id = Column(String, ForeignKey("brute_force_tasks.id"), nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    level = Column(String, nullable=False)  # INFO, WARNING, ERROR, SUCCESS
    message = Column(Text, nullable=False)
    username_attempted = Column(String, nullable=True)
    password_attempted = Column(String, nullable=True)
    
    # Relationships
    task = relationship("BruteForceTask", back_populates="logs")


# Database configuration
DATABASE_URL = "sqlite:///./brute_force.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)


def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database():
    """Initialize the database with tables."""
    print("Initializing database...")
    create_tables()
    print("Database initialized successfully!")


if __name__ == "__main__":
    init_database()
