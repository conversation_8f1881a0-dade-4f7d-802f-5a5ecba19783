#!/usr/bin/env python3
"""
Full system test for Web Brute Force API.
Tests the complete workflow including task execution.
"""

import requests
import time
import json


def submit_test_task():
    """Submit a test task."""
    print("🚀 Submitting test task...")
    
    task_data = {
        "url": "http://httpbin.org/status/200",
        "username_dict_path": "dicts/username.txt",
        "password_dict_path": "dicts/password.txt",
        "config_template": "templates/test-simple.yaml"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/tasks", json=task_data)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result["task_id"]
            print(f"✅ Task submitted: {task_id}")
            return task_id
        else:
            print(f"❌ Task submission failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error submitting task: {e}")
        return None


def monitor_task_progress(task_id, max_wait_time=60):
    """Monitor task progress until completion."""
    print(f"📊 Monitoring task progress: {task_id}")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                task = result["task"]
                
                status = task["status"]
                progress = f"{task['current_attempts']}/{task['total_attempts']}"
                
                if status != last_status:
                    print(f"📈 Status: {status}, Progress: {progress}")
                    last_status = status
                
                # Check if task is complete
                if status in ["completed", "failed", "cancelled"]:
                    print(f"🏁 Task finished with status: {status}")
                    
                    if task["found_credentials"]:
                        print("🎯 Found credentials:")
                        for result_item in result["results"]:
                            print(f"  👤 {result_item['username']}:{result_item['password']}")
                    
                    if result["recent_logs"]:
                        print("📝 Recent logs:")
                        for log in result["recent_logs"][-5:]:  # Last 5 logs
                            print(f"  [{log['level']}] {log['message']}")
                    
                    return result
                
                time.sleep(2)  # Wait 2 seconds before next check
                
            else:
                print(f"❌ Error querying task: {response.status_code}")
                break
                
        except Exception as e:
            print(f"❌ Error monitoring task: {e}")
            break
    
    print(f"⏰ Monitoring timeout after {max_wait_time} seconds")
    return None


def test_api_endpoints():
    """Test various API endpoints."""
    print("\n🧪 Testing API endpoints...")
    
    # Test root endpoint
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ Root endpoint working")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint error: {e}")
    
    # Test task list
    try:
        response = requests.get("http://localhost:8000/api/tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ Task list endpoint working: {len(tasks)} tasks")
        else:
            print(f"❌ Task list failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Task list error: {e}")


def main():
    """Main test function."""
    print("🎯 Full System Test - Web Brute Force API")
    print("=" * 60)
    
    # Test API endpoints
    test_api_endpoints()
    
    # Submit a test task
    task_id = submit_test_task()
    if not task_id:
        print("❌ Cannot proceed without successful task submission")
        return
    
    print(f"\n⏳ Waiting for executor service to pick up the task...")
    time.sleep(3)
    
    # Monitor task progress
    result = monitor_task_progress(task_id, max_wait_time=120)
    
    if result:
        print("\n" + "=" * 60)
        print("🎉 Full system test completed successfully!")
        print("\n📊 Final Results:")
        task = result["task"]
        print(f"  📋 Task ID: {task['id']}")
        print(f"  🌐 URL: {task['url']}")
        print(f"  📈 Status: {task['status']}")
        print(f"  🔢 Total Attempts: {task['total_attempts']}")
        print(f"  ✅ Successful Attempts: {task['successful_attempts']}")
        print(f"  🎯 Found Credentials: {task['found_credentials']}")
        
        if task['error_message']:
            print(f"  ❌ Error: {task['error_message']}")
    else:
        print("\n❌ Full system test failed or timed out")
    
    print("\n🔗 Useful URLs:")
    print("  📡 API Server: http://localhost:8000")
    print("  📚 API Docs: http://localhost:8000/docs")
    print("  🔄 Interactive API: http://localhost:8000/redoc")


if __name__ == "__main__":
    main()
