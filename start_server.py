#!/usr/bin/env python3
"""
Startup script for the Web Brute Force API Server.
"""

import os
import sys
import uvicorn
from models import init_database


def check_dependencies():
    """Check if all required files and directories exist."""
    required_dirs = ["dicts", "templates"]
    required_files = [
        "dicts/username.txt",
        "dicts/password.txt", 
        "templates/test-grafana.yaml"
    ]
    
    print("🔍 Checking dependencies...")
    
    # Check directories
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Missing directory: {directory}")
            return False
        else:
            print(f"✅ Directory found: {directory}")
    
    # Check files
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ Missing file: {file_path}")
            return False
        else:
            print(f"✅ File found: {file_path}")
    
    return True


def create_sample_files():
    """Create sample dictionary files if they don't exist."""
    print("📝 Creating sample files...")
    
    # Create sample username dictionary
    username_file = "dicts/username.txt"
    if not os.path.exists(username_file):
        with open(username_file, 'w', encoding='utf-8') as f:
            f.write("admin\n")
            f.write("administrator\n")
            f.write("root\n")
            f.write("user\n")
            f.write("test\n")
            f.write("guest\n")
        print(f"✅ Created sample file: {username_file}")
    
    # Create sample password dictionary
    password_file = "dicts/password.txt"
    if not os.path.exists(password_file):
        with open(password_file, 'w', encoding='utf-8') as f:
            f.write("admin\n")
            f.write("password\n")
            f.write("123456\n")
            f.write("admin123\n")
            f.write("root\n")
            f.write("test\n")
            f.write("guest\n")
        print(f"✅ Created sample file: {password_file}")


def main():
    """Main startup function."""
    print("🚀 Starting Web Brute Force API Server...")
    print("=" * 50)
    
    # Initialize database
    print("🗄️  Initializing database...")
    try:
        init_database()
        print("✅ Database initialized successfully!")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        print("\n⚠️  Some dependencies are missing.")
        print("Creating sample files...")
        create_sample_files()
        
        # Check again
        if not check_dependencies():
            print("❌ Failed to create required files. Please check permissions.")
            sys.exit(1)
    
    print("\n✅ All dependencies satisfied!")
    print("=" * 50)
    print("🌐 Starting web server...")
    print("📡 API will be available at: http://localhost:8000")
    print("📚 API documentation at: http://localhost:8000/docs")
    print("🔄 Interactive API at: http://localhost:8000/redoc")
    print("=" * 50)
    
    # Start the server
    try:
        uvicorn.run(
            "web_server:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
