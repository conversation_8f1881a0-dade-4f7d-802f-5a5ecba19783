#!/usr/bin/env python3
"""
Test script for LLM-integrated Web Brute Force API.
Tests the new URL + template submission interface.
"""

import requests
import time
import json


def test_api_connection():
    """Test basic API connection."""
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ API server is running")
            info = response.json()
            print(f"📋 Version: {info['version']}")
            print(f"📝 Description: {info['description']}")
            print(f"🔄 Workflow:")
            for step, desc in info['workflow'].items():
                print(f"  {step}. {desc}")
            return True
        else:
            print(f"❌ API server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Is it running?")
        return False
    except Exception as e:
        print(f"❌ Error testing API connection: {e}")
        return False


def create_sample_templates():
    """Create sample LLM-generated templates for testing."""
    templates = {
        "simple_login": """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "input[name='username'], input[type='email'], #username, #email"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "input[name='password'], input[type='password'], #password"
    parameters:
      text: "{password}"
  
  submit_form:
    action: click
    target:
      selector: "button[type='submit'], input[type='submit'], .login-btn, #login"
  
  check_result:
    action: check
    success:
      - ".dashboard"
      - ".welcome"
      - "text=Welcome"
    failure:
      - ".error"
      - ".alert-danger"
      - "text=Invalid"
      - "text=Error"
""",
        
        "admin_panel": """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: load
  
  fill_username:
    action: type
    target:
      selector: "#admin_user, input[name='admin_username'], .admin-login input[type='text']"
    parameters:
      text: "{username}"
  
  fill_password:
    action: type
    target:
      selector: "#admin_pass, input[name='admin_password'], .admin-login input[type='password']"
    parameters:
      text: "{password}"
  
  click_login:
    action: click
    target:
      selector: "#admin_login, .admin-submit, button.admin-btn"
  
  check_result:
    action: check
    success:
      - ".admin-dashboard"
      - ".control-panel"
      - "text=Admin Panel"
    failure:
      - ".login-error"
      - "text=Access Denied"
      - "text=Invalid credentials"
""",
        
        "basic_form": """
steps:
  navigate:
    action: navigate
    parameters:
      url: "{url}"
      waitUntil: networkidle
  
  type_username:
    action: type
    target:
      selector: "input[type='text']:first, input[name*='user']:first"
    parameters:
      text: "{username}"
  
  type_password:
    action: type
    target:
      selector: "input[type='password']:first"
    parameters:
      text: "{password}"
  
  submit:
    action: click
    target:
      selector: "form button, form input[type='submit']"
  
  verify:
    action: check
    success:
      - "body:not(:contains('login'))"
      - ".success"
    failure:
      - ".error"
      - "text=failed"
"""
    }
    
    return templates


def test_llm_task_submission():
    """Test task submission with LLM-generated templates."""
    print("\n🚀 Testing LLM-integrated task submission...")
    
    templates = create_sample_templates()
    test_cases = [
        {
            "url": "http://httpbin.org/status/200",
            "template": templates["simple_login"],
            "description": "Simple login form template"
        },
        {
            "url": "https://httpbin.org/basic-auth/user/pass",
            "template": templates["admin_panel"],
            "description": "Admin panel template"
        },
        {
            "url": "http://example.com/login",
            "template": templates["basic_form"],
            "description": "Basic form template"
        }
    ]
    
    submitted_tasks = []
    
    for case in test_cases:
        print(f"\n📋 Submitting task:")
        print(f"  🌐 URL: {case['url']}")
        print(f"  📝 Template: {case['description']}")
        
        task_data = {
            "url": case["url"],
            "template": case["template"]
        }
        
        try:
            response = requests.post("http://localhost:8000/api/tasks", json=task_data)
            
            if response.status_code == 200:
                result = response.json()
                task_id = result["task_id"]
                print(f"✅ Task submitted successfully: {task_id}")
                print(f"📊 Configuration:")
                config = result["configuration"]
                print(f"  👤 Username dict: {config['username_dict']}")
                print(f"  🔑 Password dict: {config['password_dict']}")
                print(f"  📋 Template file: {config['config_template']}")
                print(f"💬 {result['message']}")
                
                submitted_tasks.append({
                    "task_id": task_id,
                    "url": case["url"],
                    "template_file": config['config_template'],
                    "result": result
                })
            else:
                print(f"❌ Task submission failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error submitting task: {e}")
    
    return submitted_tasks


def test_template_validation():
    """Test template validation with invalid YAML."""
    print("\n🧪 Testing template validation...")
    
    invalid_cases = [
        {
            "url": "http://example.com",
            "template": "invalid: yaml: content: [unclosed",
            "description": "Invalid YAML syntax"
        },
        {
            "url": "http://example.com",
            "template": "",
            "description": "Empty template"
        }
    ]
    
    for case in invalid_cases:
        print(f"\n🔬 Testing: {case['description']}")
        
        try:
            response = requests.post("http://localhost:8000/api/tasks", json=case)
            
            if response.status_code == 400:
                print(f"  ✅ Correctly rejected with 400 error")
                error = response.json()
                print(f"     Error: {error.get('detail', 'No detail')}")
            else:
                print(f"  ⚠️  Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")


def test_task_monitoring(tasks):
    """Test task monitoring for submitted tasks."""
    if not tasks:
        print("\n📭 No tasks to monitor")
        return
    
    print(f"\n📊 Monitoring {len(tasks)} submitted tasks...")
    
    for task_info in tasks:
        task_id = task_info["task_id"]
        url = task_info["url"]
        template_file = task_info["template_file"]
        
        print(f"\n🔍 Checking task: {task_id[:8]}...")
        print(f"  🌐 URL: {url}")
        print(f"  📋 Template: {template_file}")
        
        try:
            response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                task = result["task"]
                
                print(f"  📈 Status: {task['status']}")
                print(f"  📊 Progress: {task['current_attempts']}/{task['total_attempts']}")
                print(f"  🎯 Found credentials: {task['found_credentials']}")
                
                if task['error_message']:
                    print(f"  ❌ Error: {task['error_message']}")
                
                if result["recent_logs"]:
                    print(f"  📝 Recent logs:")
                    for log in result["recent_logs"][-2:]:  # Last 2 logs
                        print(f"    [{log['level']}] {log['message']}")
                        
            else:
                print(f"  ❌ Error querying task: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error monitoring task: {e}")


def main():
    """Main test function."""
    print("🎯 LLM-Integrated Web Brute Force API Test Suite")
    print("=" * 65)
    print("Testing the new URL + LLM-generated template interface")
    print("=" * 65)
    
    # Test API connection
    if not test_api_connection():
        print("\n❌ API server is not accessible. Please start it first:")
        print("   python start_services.py")
        return
    
    # Test LLM task submission
    submitted_tasks = test_llm_task_submission()
    
    # Test template validation
    test_template_validation()
    
    # Wait for executor to pick up tasks
    if submitted_tasks:
        print(f"\n⏳ Waiting 5 seconds for executor service to process tasks...")
        time.sleep(5)
        
        # Test task monitoring
        test_task_monitoring(submitted_tasks)
    
    print("\n" + "=" * 65)
    print("🎉 LLM-integrated API tests completed!")
    print("\n📚 Key features tested:")
    print("  ✅ URL + template submission")
    print("  ✅ Template validation and storage")
    print("  ✅ Automatic template file generation")
    print("  ✅ Task monitoring and progress tracking")
    
    print(f"\n🔗 API Documentation:")
    print(f"  📡 API Server: http://localhost:8000")
    print(f"  📚 Swagger Docs: http://localhost:8000/docs")
    print(f"  🔄 ReDoc: http://localhost:8000/redoc")


if __name__ == "__main__":
    main()
