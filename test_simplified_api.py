#!/usr/bin/env python3
"""
Test script for simplified Web Brute Force API.
Tests the new URL-only submission interface.
"""

import requests
import time
import json


def test_api_connection():
    """Test basic API connection."""
    try:
        response = requests.get("http://localhost:8000/")
        if response.status_code == 200:
            print("✅ API server is running")
            info = response.json()
            print(f"📋 Version: {info['version']}")
            print(f"📝 Description: {info['description']}")
            return True
        else:
            print(f"❌ API server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server. Is it running?")
        return False
    except Exception as e:
        print(f"❌ Error testing API connection: {e}")
        return False


def test_simplified_task_submission():
    """Test simplified task submission with only URL."""
    print("\n🚀 Testing simplified task submission...")
    
    # Test with different URLs
    test_urls = [
        "http://httpbin.org/status/200",
        "https://httpbin.org/basic-auth/user/pass",
        "http://example.com/login"
    ]
    
    submitted_tasks = []
    
    for url in test_urls:
        print(f"\n📋 Submitting task for: {url}")
        
        # Simple JSON payload with only URL
        task_data = {"url": url}
        
        try:
            response = requests.post("http://localhost:8000/api/tasks", json=task_data)
            
            if response.status_code == 200:
                result = response.json()
                task_id = result["task_id"]
                print(f"✅ Task submitted successfully: {task_id}")
                print(f"📊 Configuration used:")
                config = result["configuration"]
                print(f"  👤 Username dict: {config['username_dict']}")
                print(f"  🔑 Password dict: {config['password_dict']}")
                print(f"  📋 Config template: {config['config_template']}")
                print(f"💬 Message: {result['message']}")
                
                submitted_tasks.append({
                    "task_id": task_id,
                    "url": url,
                    "result": result
                })
            else:
                print(f"❌ Task submission failed: {response.status_code}")
                print(f"📋 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error submitting task: {e}")
    
    return submitted_tasks


def test_task_monitoring(tasks):
    """Test task monitoring for submitted tasks."""
    print(f"\n📊 Monitoring {len(tasks)} submitted tasks...")
    
    for task_info in tasks:
        task_id = task_info["task_id"]
        url = task_info["url"]
        
        print(f"\n🔍 Checking task: {task_id[:8]}... ({url})")
        
        try:
            response = requests.get(f"http://localhost:8000/api/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                task = result["task"]
                
                print(f"  📈 Status: {task['status']}")
                print(f"  📊 Progress: {task['current_attempts']}/{task['total_attempts']}")
                print(f"  🎯 Found credentials: {task['found_credentials']}")
                
                if task['error_message']:
                    print(f"  ❌ Error: {task['error_message']}")
                
                if result["recent_logs"]:
                    print(f"  📝 Recent logs:")
                    for log in result["recent_logs"][-2:]:  # Last 2 logs
                        print(f"    [{log['level']}] {log['message']}")
                        
            else:
                print(f"  ❌ Error querying task: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ Error monitoring task: {e}")


def test_invalid_submissions():
    """Test invalid submission scenarios."""
    print("\n🧪 Testing invalid submissions...")
    
    invalid_cases = [
        {"data": {}, "description": "Empty payload"},
        {"data": {"url": ""}, "description": "Empty URL"},
        {"data": {"url": "not-a-url"}, "description": "Invalid URL format"},
        {"data": {"url": "ftp://example.com"}, "description": "Non-HTTP URL"},
        {"data": {"url": "http://example.com", "extra": "field"}, "description": "Extra fields (should be ignored)"}
    ]
    
    for case in invalid_cases:
        print(f"\n🔬 Testing: {case['description']}")
        
        try:
            response = requests.post("http://localhost:8000/api/tasks", json=case["data"])
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ Accepted (task_id: {result['task_id'][:8]}...)")
            elif response.status_code == 422:
                print(f"  ❌ Validation error (expected): {response.status_code}")
            else:
                print(f"  ⚠️  Unexpected status: {response.status_code}")
                print(f"     Response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")


def test_task_listing():
    """Test task listing endpoint."""
    print("\n📋 Testing task listing...")
    
    try:
        response = requests.get("http://localhost:8000/api/tasks")
        
        if response.status_code == 200:
            tasks = response.json()
            print(f"✅ Retrieved {len(tasks)} tasks")
            
            if tasks:
                print("📊 Recent tasks:")
                for i, task in enumerate(tasks[:3], 1):  # Show first 3
                    status_emoji = {
                        "pending": "⏳",
                        "running": "🔄", 
                        "completed": "✅",
                        "failed": "❌",
                        "cancelled": "🚫"
                    }.get(task['status'], "❓")
                    
                    print(f"  {i}. {status_emoji} {task['id'][:8]}... - {task['status']} - {task['url']}")
            else:
                print("📭 No tasks found")
                
        else:
            print(f"❌ Task listing failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error listing tasks: {e}")


def main():
    """Main test function."""
    print("🎯 Simplified Web Brute Force API Test Suite")
    print("=" * 60)
    print("Testing the new URL-only submission interface")
    print("=" * 60)
    
    # Test API connection
    if not test_api_connection():
        print("\n❌ API server is not accessible. Please start it first:")
        print("   python start_services.py")
        return
    
    # Test simplified task submission
    submitted_tasks = test_simplified_task_submission()
    
    if not submitted_tasks:
        print("\n❌ No tasks were submitted successfully")
        return
    
    # Wait for executor to pick up tasks
    print(f"\n⏳ Waiting 5 seconds for executor service to process tasks...")
    time.sleep(5)
    
    # Test task monitoring
    test_task_monitoring(submitted_tasks)
    
    # Test invalid submissions
    test_invalid_submissions()
    
    # Test task listing
    test_task_listing()
    
    print("\n" + "=" * 60)
    print("🎉 Simplified API tests completed!")
    print("\n📚 Key improvements:")
    print("  ✅ Users only need to provide target URL")
    print("  ✅ Configuration is automatically selected")
    print("  ✅ Ready for LLM-generated config templates")
    print("  ✅ Simplified integration for client applications")
    
    print(f"\n🔗 API Documentation:")
    print(f"  📡 API Server: http://localhost:8000")
    print(f"  📚 Swagger Docs: http://localhost:8000/docs")
    print(f"  🔄 ReDoc: http://localhost:8000/redoc")


if __name__ == "__main__":
    main()
